const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Create database file path
const dbPath = path.join(__dirname, 'healthcare.db');

// Create and connect to SQLite database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error connecting to SQLite database:', err.message);
  } else {
    console.log('✅ Connected to SQLite database at:', dbPath);
  }
});

// Enable foreign keys
db.run('PRAGMA foreign_keys = ON');

// Create tables
const createTables = () => {
  console.log('🏗️ Creating database tables...');

  // Create patients table
  db.run(`
    CREATE TABLE IF NOT EXISTS patients (
      nationalId TEXT PRIMARY KEY,
      firstName TEXT NOT NULL,
      lastName TEXT NOT NULL,
      dateOfBirth DATE,
      gender TEXT,
      phone TEXT,
      email TEXT,
      address TEXT,
      emergencyContact TEXT,
      emergencyPhone TEXT,
      bloodType TEXT,
      allergies TEXT,
      medicalHistory TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating patients table:', err.message);
    } else {
      console.log('✅ Patients table created successfully');
    }
  });

  // Create doctors table
  db.run(`
    CREATE TABLE IF NOT EXISTS doctors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      firstName TEXT NOT NULL,
      lastName TEXT NOT NULL,
      specialization TEXT,
      department TEXT,
      phone TEXT,
      email TEXT,
      licenseNumber TEXT,
      experience INTEGER,
      education TEXT,
      schedule TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating doctors table:', err.message);
    } else {
      console.log('✅ Doctors table created successfully');
    }
  });

  // Create bills table
  db.run(`
    CREATE TABLE IF NOT EXISTS bills (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      billNumber TEXT UNIQUE NOT NULL,
      patientId TEXT NOT NULL,
      patientName TEXT NOT NULL,
      patientPhone TEXT,
      patientEmail TEXT,
      doctorId INTEGER,
      doctorName TEXT,
      department TEXT,
      billDate DATE NOT NULL,
      dueDate DATE,
      subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
      taxAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
      discountAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
      totalAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
      paidAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
      balanceAmount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
      status TEXT DEFAULT 'pending' CHECK (status IN ('draft', 'pending', 'paid', 'partially_paid', 'overdue', 'cancelled')),
      paymentMethod TEXT CHECK (paymentMethod IN ('cash', 'card', 'insurance', 'mobile_money', 'bank_transfer')),
      paymentDate DATE,
      paymentReference TEXT,
      insuranceProvider TEXT,
      insurancePolicyNumber TEXT,
      insuranceCoverage DECIMAL(5,2),
      notes TEXT,
      createdBy TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (doctorId) REFERENCES doctors(id) ON DELETE SET NULL
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating bills table:', err.message);
    } else {
      console.log('✅ Bills table created successfully');
    }
  });

  // Create bill_items table
  db.run(`
    CREATE TABLE IF NOT EXISTS bill_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      billId INTEGER NOT NULL,
      itemType TEXT NOT NULL CHECK (itemType IN ('consultation', 'procedure', 'medication', 'lab_test', 'imaging', 'room_charge', 'other')),
      itemName TEXT NOT NULL,
      itemDescription TEXT,
      quantity INTEGER NOT NULL DEFAULT 1,
      unitPrice DECIMAL(10,2) NOT NULL,
      totalPrice DECIMAL(10,2) NOT NULL,
      discountPercent DECIMAL(5,2) DEFAULT 0.00,
      discountAmount DECIMAL(10,2) DEFAULT 0.00,
      finalAmount DECIMAL(10,2) NOT NULL,
      serviceDate DATE,
      providedBy TEXT,
      notes TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (billId) REFERENCES bills(id) ON DELETE CASCADE
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating bill_items table:', err.message);
    } else {
      console.log('✅ Bill items table created successfully');
    }
  });

  // Insert sample data
  insertSampleData();
};

// Insert sample data
const insertSampleData = () => {
  console.log('📊 Inserting sample data...');

  // Sample patients
  const samplePatients = [
    ['1234567890123456', 'John', 'Doe', '1990-01-15', 'Male', '+250788123456', '<EMAIL>', 'Kigali, Rwanda', 'Jane Doe', '+250788123457', 'O+', 'None', 'No significant medical history'],
    ['9876543210987654', 'Jane', 'Smith', '1985-05-20', 'Female', '+250788654321', '<EMAIL>', 'Kigali, Rwanda', 'John Smith', '+250788654322', 'A+', 'Penicillin', 'Hypertension'],
    ['5555666677778888', 'Robert', 'Brown', '1978-12-10', 'Male', '+250788999888', '<EMAIL>', 'Kigali, Rwanda', 'Mary Brown', '+250788999889', 'B+', 'None', 'Diabetes Type 2']
  ];

  samplePatients.forEach(patient => {
    db.run(`
      INSERT OR IGNORE INTO patients 
      (nationalId, firstName, lastName, dateOfBirth, gender, phone, email, address, emergencyContact, emergencyPhone, bloodType, allergies, medicalHistory)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, patient, (err) => {
      if (err) {
        console.error('❌ Error inserting patient:', err.message);
      }
    });
  });

  // Sample doctors
  const sampleDoctors = [
    ['Smith', 'Johnson', 'General Medicine', 'General Medicine', '+250788111222', '<EMAIL>', 'MD12345', 10, 'MD from University of Rwanda', 'Mon-Fri 8AM-5PM'],
    ['Mary', 'Wilson', 'Cardiology', 'Cardiology', '+250788333444', '<EMAIL>', 'MD67890', 15, 'MD, Cardiology Specialist', 'Mon-Wed-Fri 9AM-4PM'],
    ['James', 'Lee', 'Emergency Medicine', 'Emergency', '+250788555666', '<EMAIL>', 'MD11111', 8, 'MD, Emergency Medicine', '24/7 On-call']
  ];

  sampleDoctors.forEach(doctor => {
    db.run(`
      INSERT OR IGNORE INTO doctors 
      (firstName, lastName, specialization, department, phone, email, licenseNumber, experience, education, schedule)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, doctor, (err) => {
      if (err) {
        console.error('❌ Error inserting doctor:', err.message);
      }
    });
  });

  console.log('✅ Sample data inserted successfully');
};

// Initialize database
createTables();

module.exports = db;
