[{"B:\\Done projects\\Healthcare\\frontend\\src\\index.js": "1", "B:\\Done projects\\Healthcare\\frontend\\src\\App.js": "2", "B:\\Done projects\\Healthcare\\frontend\\src\\reportWebVitals.js": "3", "B:\\Done projects\\Healthcare\\frontend\\src\\components\\Login.jsx": "4", "B:\\Done projects\\Healthcare\\frontend\\src\\context\\AuthContext.jsx": "5", "B:\\Done projects\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx": "6", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Home.jsx": "7", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\About.jsx": "8", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Help.jsx": "9", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Exams.jsx": "10", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Doctor.jsx": "11", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Patients.jsx": "12", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Messages.jsx": "13", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx": "14", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx": "15", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Room.jsx": "16", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Appointment.jsx": "17", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx": "18", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx": "19", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicExamResults.jsx": "20", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicMedicalRecords.jsx": "21", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PatientPortal.jsx": "22", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicPharmacy.jsx": "23", "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicAppointment.jsx": "24", "B:\\Done projects\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx": "25", "B:\\Done projects\\Healthcare\\frontend\\src\\components\\BillingForm.jsx": "26", "B:\\Done projects\\Healthcare\\frontend\\src\\components\\FlutterwavePayment.jsx": "27"}, {"size": 651, "mtime": 1749732545518, "results": "28", "hashOfConfig": "29"}, {"size": 22990, "mtime": 1749742540267, "results": "30", "hashOfConfig": "29"}, {"size": 362, "mtime": 1747922953071, "results": "31", "hashOfConfig": "29"}, {"size": 5673, "mtime": 1749732001525, "results": "32", "hashOfConfig": "29"}, {"size": 5954, "mtime": 1749732599998, "results": "33", "hashOfConfig": "29"}, {"size": 6790, "mtime": 1748766718790, "results": "34", "hashOfConfig": "29"}, {"size": 28555, "mtime": 1749741832994, "results": "35", "hashOfConfig": "29"}, {"size": 13771, "mtime": 1748251298811, "results": "36", "hashOfConfig": "29"}, {"size": 17390, "mtime": 1748251195090, "results": "37", "hashOfConfig": "29"}, {"size": 30709, "mtime": 1748272681020, "results": "38", "hashOfConfig": "29"}, {"size": 21102, "mtime": 1748338142433, "results": "39", "hashOfConfig": "29"}, {"size": 42181, "mtime": 1748725421880, "results": "40", "hashOfConfig": "29"}, {"size": 23474, "mtime": 1748712959569, "results": "41", "hashOfConfig": "29"}, {"size": 31971, "mtime": 1748631188267, "results": "42", "hashOfConfig": "29"}, {"size": 30067, "mtime": 1748630180954, "results": "43", "hashOfConfig": "29"}, {"size": 39141, "mtime": 1748336567706, "results": "44", "hashOfConfig": "29"}, {"size": 24095, "mtime": 1748427457298, "results": "45", "hashOfConfig": "29"}, {"size": 20503, "mtime": 1749829496801, "results": "46", "hashOfConfig": "29"}, {"size": 139033, "mtime": 1749731056831, "results": "47", "hashOfConfig": "29"}, {"size": 14660, "mtime": 1749739163239, "results": "48", "hashOfConfig": "29"}, {"size": 29552, "mtime": 1749741246396, "results": "49", "hashOfConfig": "29"}, {"size": 15073, "mtime": 1749738333317, "results": "50", "hashOfConfig": "29"}, {"size": 29465, "mtime": 1749742385566, "results": "51", "hashOfConfig": "29"}, {"size": 14015, "mtime": 1749739242590, "results": "52", "hashOfConfig": "29"}, {"size": 11421, "mtime": 1749545947834, "results": "53", "hashOfConfig": "29"}, {"size": 24479, "mtime": 1749916463478, "results": "54", "hashOfConfig": "29"}, {"size": 14281, "mtime": 1749916409274, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g5h2s", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "B:\\Done projects\\Healthcare\\frontend\\src\\index.js", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\App.js", ["137"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\reportWebVitals.js", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\components\\Login.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\context\\AuthContext.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Home.jsx", ["138", "139", "140", "141", "142", "143", "144", "145"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\About.jsx", ["146", "147", "148", "149", "150", "151", "152", "153"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Help.jsx", ["154", "155", "156", "157", "158", "159", "160", "161"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Exams.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Doctor.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Patients.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Messages.jsx", ["162"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Room.jsx", ["163"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\Appointment.jsx", ["164"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx", ["165"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicExamResults.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicMedicalRecords.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PatientPortal.jsx", ["166", "167", "168", "169"], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicPharmacy.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\pages\\PublicAppointment.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\components\\BillingForm.jsx", [], [], "B:\\Done projects\\Healthcare\\frontend\\src\\components\\FlutterwavePayment.jsx", [], [], {"ruleId": "170", "severity": 1, "message": "171", "line": 54, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 54, "endColumn": 24}, {"ruleId": "174", "severity": 1, "message": "175", "line": 386, "column": 21, "nodeType": "176", "endLine": 386, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 387, "column": 21, "nodeType": "176", "endLine": 387, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 388, "column": 21, "nodeType": "176", "endLine": 388, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 389, "column": 21, "nodeType": "176", "endLine": 389, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 396, "column": 21, "nodeType": "176", "endLine": 396, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 397, "column": 21, "nodeType": "176", "endLine": 397, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 398, "column": 21, "nodeType": "176", "endLine": 398, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 399, "column": 21, "nodeType": "176", "endLine": 399, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 212, "column": 21, "nodeType": "176", "endLine": 212, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 213, "column": 21, "nodeType": "176", "endLine": 213, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 214, "column": 21, "nodeType": "176", "endLine": 214, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 215, "column": 21, "nodeType": "176", "endLine": 215, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 222, "column": 21, "nodeType": "176", "endLine": 222, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 223, "column": 21, "nodeType": "176", "endLine": 223, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 224, "column": 21, "nodeType": "176", "endLine": 224, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 225, "column": 21, "nodeType": "176", "endLine": 225, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 266, "column": 21, "nodeType": "176", "endLine": 266, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 267, "column": 21, "nodeType": "176", "endLine": 267, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 268, "column": 21, "nodeType": "176", "endLine": 268, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 269, "column": 21, "nodeType": "176", "endLine": 269, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 276, "column": 21, "nodeType": "176", "endLine": 276, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 277, "column": 21, "nodeType": "176", "endLine": 277, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 278, "column": 21, "nodeType": "176", "endLine": 278, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 279, "column": 21, "nodeType": "176", "endLine": 279, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 32, "column": 6, "nodeType": "179", "endLine": 32, "endColumn": 23, "suggestions": "180"}, {"ruleId": "170", "severity": 1, "message": "181", "line": 18, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 18, "endColumn": 22}, {"ruleId": "170", "severity": 1, "message": "182", "line": 9, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 9, "endColumn": 18}, {"ruleId": "170", "severity": 1, "message": "183", "line": 9, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 9, "endColumn": 18}, {"ruleId": "174", "severity": 1, "message": "175", "line": 221, "column": 21, "nodeType": "176", "endLine": 221, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 222, "column": 21, "nodeType": "176", "endLine": 222, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 223, "column": 21, "nodeType": "176", "endLine": 223, "endColumn": 94}, {"ruleId": "174", "severity": 1, "message": "175", "line": 224, "column": 21, "nodeType": "176", "endLine": 224, "endColumn": 94}, "no-unused-vars", "'isPatientPortal' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchMessages', 'fetchUnreadCount', and 'fetchUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["184"], "'selectedRoom' is assigned a value but never used.", "'showForm' is assigned a value but never used.", "'patients' is assigned a value but never used.", {"desc": "185", "fix": "186"}, "Update the dependencies array to be: [user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", {"range": "187", "text": "188"}, [916, 933], "[user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]"]