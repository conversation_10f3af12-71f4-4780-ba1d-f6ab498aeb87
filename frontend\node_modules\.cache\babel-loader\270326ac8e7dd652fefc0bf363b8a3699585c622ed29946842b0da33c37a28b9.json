{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Healthcare\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.jsx\",\n  _s = $RefreshSig$();\nimport { useAuth } from '../context/AuthContext';\nimport Login from './Login';\nimport { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children,\n  requireAdmin = false,\n  allowedRoles = []\n}) {\n  _s();\n  const {\n    isAuthenticated,\n    isAdmin,\n    loading,\n    login,\n    user\n  } = useAuth();\n  const [showLogin, setShowLogin] = useState(false);\n\n  // Handle successful login\n  const handleLogin = userData => {\n    login(userData);\n    setShowLogin(false);\n  };\n\n  // Check if user has required role\n  const hasRequiredRole = () => {\n    if (!user) return false;\n\n    // If specific roles are required, check if user has one of them\n    if (allowedRoles.length > 0) {\n      return allowedRoles.includes(user.role);\n    }\n\n    // If admin is required, check admin status\n    if (requireAdmin) {\n      return isAdmin();\n    }\n\n    // Default: any authenticated user can access\n    return true;\n  };\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If not authenticated, show login screen\n  if (!isAuthenticated()) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900\",\n                children: \"\\uD83C\\uDFE5 Healthcare Portal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mt-1\",\n                children: \"Secure Medical Management System\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-[calc(100vh-120px)] px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-xl p-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-20 h-20 bg-gradient-to-br from-blue-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-10 h-10 text-white\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold text-gray-900 mb-3\",\n              children: \"Welcome Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-8 text-lg\",\n              children: \"Please sign in to access the healthcare portal and manage patient care\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLogin(true),\n              className: \"w-full bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 text-white font-bold py-4 px-6 rounded-xl text-lg transition-all transform hover:scale-105 shadow-lg hover:shadow-xl\",\n              children: \"\\uD83D\\uDD10 Sign In to Continue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), showLogin && /*#__PURE__*/_jsxDEV(Login, {\n        onLogin: handleLogin,\n        onClose: () => setShowLogin(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 w-full bg-white border-t border-gray-200 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Secure Healthcare Portal \\u2022 Protected by SSL \\u2022 HIPAA Compliant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If user doesn't have required role, show access denied\n  if (isAuthenticated() && !hasRequiredRole()) {\n    const getAccessMessage = () => {\n      if (requireAdmin) {\n        return \"Admin privileges required to access this page.\";\n      }\n      if (allowedRoles.length > 0) {\n        return `This page is restricted to: ${allowedRoles.join(', ')} roles only.`;\n      }\n      return \"You don't have permission to access this page.\";\n    };\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-8 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-10 h-10 text-red-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-3\",\n            children: \"Access Denied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-2\",\n              children: getAccessMessage()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-3 mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Your Role:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"capitalize text-blue-600\",\n                  children: user === null || user === void 0 ? void 0 : user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 47\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), allowedRoles.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Required Roles:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: allowedRoles.join(', ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 54\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.history.back(),\n              className: \"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105\",\n              children: \"\\uD83D\\uDD19 Go Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/',\n              className: \"w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-all\",\n              children: \"\\uD83C\\uDFE0 Go to Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If authenticated and has required permissions, render children\n  return children;\n}\n_s(ProtectedRoute, \"lbisgZGpbSsC3QtDzqjhNBu7RlM=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["useAuth", "<PERSON><PERSON>", "useState", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requireAdmin", "allowedRoles", "_s", "isAuthenticated", "isAdmin", "loading", "login", "user", "showLogin", "setShow<PERSON><PERSON>in", "handleLogin", "userData", "hasRequiredRole", "length", "includes", "role", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "onLogin", "onClose", "getAccessMessage", "join", "window", "history", "back", "location", "href", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Healthcare/frontend/src/components/ProtectedRoute.jsx"], "sourcesContent": ["import { useAuth } from '../context/AuthContext';\nimport Login from './Login';\nimport { useState } from 'react';\n\nfunction ProtectedRoute({ children, requireAdmin = false, allowedRoles = [] }) {\n  const { isAuthenticated, isAdmin, loading, login, user } = useAuth();\n  const [showLogin, setShowLogin] = useState(false);\n\n  // Handle successful login\n  const handleLogin = (userData) => {\n    login(userData);\n    setShowLogin(false);\n  };\n\n  // Check if user has required role\n  const hasRequiredRole = () => {\n    if (!user) return false;\n\n    // If specific roles are required, check if user has one of them\n    if (allowedRoles.length > 0) {\n      return allowedRoles.includes(user.role);\n    }\n\n    // If admin is required, check admin status\n    if (requireAdmin) {\n      return isAdmin();\n    }\n\n    // Default: any authenticated user can access\n    return true;\n  };\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // If not authenticated, show login screen\n  if (!isAuthenticated()) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\">\n        {/* Header */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"max-w-7xl mx-auto px-4 py-6\">\n            <div className=\"flex items-center justify-center\">\n              <div className=\"text-center\">\n                <h1 className=\"text-3xl font-bold text-gray-900\">🏥 Healthcare Portal</h1>\n                <p className=\"text-gray-600 mt-1\">Secure Medical Management System</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Login Content */}\n        <div className=\"flex items-center justify-center min-h-[calc(100vh-120px)] px-4\">\n          <div className=\"max-w-md w-full\">\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 text-center\">\n              <div className=\"w-20 h-20 bg-gradient-to-br from-blue-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-10 h-10 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-3\">Welcome Back</h2>\n              <p className=\"text-gray-600 mb-8 text-lg\">\n                Please sign in to access the healthcare portal and manage patient care\n              </p>\n              <button\n                onClick={() => setShowLogin(true)}\n                className=\"w-full bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 text-white font-bold py-4 px-6 rounded-xl text-lg transition-all transform hover:scale-105 shadow-lg hover:shadow-xl\"\n              >\n                🔐 Sign In to Continue\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Login Modal */}\n        {showLogin && (\n          <Login\n            onLogin={handleLogin}\n            onClose={() => setShowLogin(false)}\n          />\n        )}\n\n        {/* Footer */}\n        <div className=\"absolute bottom-0 w-full bg-white border-t border-gray-200 py-4\">\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-500\">\n              Secure Healthcare Portal • Protected by SSL • HIPAA Compliant\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // If user doesn't have required role, show access denied\n  if (isAuthenticated() && !hasRequiredRole()) {\n    const getAccessMessage = () => {\n      if (requireAdmin) {\n        return \"Admin privileges required to access this page.\";\n      }\n      if (allowedRoles.length > 0) {\n        return `This page is restricted to: ${allowedRoles.join(', ')} roles only.`;\n      }\n      return \"You don't have permission to access this page.\";\n    };\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center\">\n        <div className=\"max-w-md w-full mx-4\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 text-center\">\n            <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <svg className=\"w-10 h-10 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728\" />\n              </svg>\n            </div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-3\">Access Denied</h2>\n            <div className=\"mb-6\">\n              <p className=\"text-gray-600 mb-2\">{getAccessMessage()}</p>\n              <div className=\"bg-gray-50 rounded-lg p-3 mt-4\">\n                <p className=\"text-sm text-gray-700\">\n                  <strong>Your Role:</strong> <span className=\"capitalize text-blue-600\">{user?.role}</span>\n                </p>\n                {allowedRoles.length > 0 && (\n                  <p className=\"text-sm text-gray-700 mt-1\">\n                    <strong>Required Roles:</strong> <span className=\"text-green-600\">{allowedRoles.join(', ')}</span>\n                  </p>\n                )}\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <button\n                onClick={() => window.history.back()}\n                className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105\"\n              >\n                🔙 Go Back\n              </button>\n              <button\n                onClick={() => window.location.href = '/'}\n                className=\"w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-all\"\n              >\n                🏠 Go to Home\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // If authenticated and has required permissions, render children\n  return children;\n}\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,cAAcA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG,KAAK;EAAEC,YAAY,GAAG;AAAG,CAAC,EAAE;EAAAC,EAAA;EAC7E,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EACpE,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMe,WAAW,GAAIC,QAAQ,IAAK;IAChCL,KAAK,CAACK,QAAQ,CAAC;IACfF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACL,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIN,YAAY,CAACY,MAAM,GAAG,CAAC,EAAE;MAC3B,OAAOZ,YAAY,CAACa,QAAQ,CAACP,IAAI,CAACQ,IAAI,CAAC;IACzC;;IAEA;IACA,IAAIf,YAAY,EAAE;MAChB,OAAOI,OAAO,CAAC,CAAC;IAClB;;IAEA;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,IAAIC,OAAO,EAAE;IACX,oBACER,OAAA;MAAKmB,SAAS,EAAC,oGAAoG;MAAAjB,QAAA,eACjHF,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAjB,QAAA,gBAC1BF,OAAA;UAAKmB,SAAS,EAAC;QAA6E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGvB,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAjB,QAAA,EAAC;QAAU;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACjB,eAAe,CAAC,CAAC,EAAE;IACtB,oBACEN,OAAA;MAAKmB,SAAS,EAAC,mEAAmE;MAAAjB,QAAA,gBAEhFF,OAAA;QAAKmB,SAAS,EAAC,6CAA6C;QAAAjB,QAAA,eAC1DF,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAAjB,QAAA,eAC1CF,OAAA;YAAKmB,SAAS,EAAC,kCAAkC;YAAAjB,QAAA,eAC/CF,OAAA;cAAKmB,SAAS,EAAC,aAAa;cAAAjB,QAAA,gBAC1BF,OAAA;gBAAImB,SAAS,EAAC,kCAAkC;gBAAAjB,QAAA,EAAC;cAAoB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EvB,OAAA;gBAAGmB,SAAS,EAAC,oBAAoB;gBAAAjB,QAAA,EAAC;cAAgC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKmB,SAAS,EAAC,iEAAiE;QAAAjB,QAAA,eAC9EF,OAAA;UAAKmB,SAAS,EAAC,iBAAiB;UAAAjB,QAAA,eAC9BF,OAAA;YAAKmB,SAAS,EAAC,gDAAgD;YAAAjB,QAAA,gBAC7DF,OAAA;cAAKmB,SAAS,EAAC,mHAAmH;cAAAjB,QAAA,eAChIF,OAAA;gBAAKmB,SAAS,EAAC,sBAAsB;gBAACK,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAxB,QAAA,eACzFF,OAAA;kBAAM2B,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3K;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAImB,SAAS,EAAC,uCAAuC;cAAAjB,QAAA,EAAC;YAAY;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEvB,OAAA;cAAGmB,SAAS,EAAC,4BAA4B;cAAAjB,QAAA,EAAC;YAE1C;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvB,OAAA;cACE+B,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAAC,IAAI,CAAE;cAClCO,SAAS,EAAC,gNAAgN;cAAAjB,QAAA,EAC3N;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLZ,SAAS,iBACRX,OAAA,CAACH,KAAK;QACJmC,OAAO,EAAEnB,WAAY;QACrBoB,OAAO,EAAEA,CAAA,KAAMrB,YAAY,CAAC,KAAK;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAGDvB,OAAA;QAAKmB,SAAS,EAAC,iEAAiE;QAAAjB,QAAA,eAC9EF,OAAA;UAAKmB,SAAS,EAAC,aAAa;UAAAjB,QAAA,eAC1BF,OAAA;YAAGmB,SAAS,EAAC,uBAAuB;YAAAjB,QAAA,EAAC;UAErC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIjB,eAAe,CAAC,CAAC,IAAI,CAACS,eAAe,CAAC,CAAC,EAAE;IAC3C,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAI/B,YAAY,EAAE;QAChB,OAAO,gDAAgD;MACzD;MACA,IAAIC,YAAY,CAACY,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAO,+BAA+BZ,YAAY,CAAC+B,IAAI,CAAC,IAAI,CAAC,cAAc;MAC7E;MACA,OAAO,gDAAgD;IACzD,CAAC;IAED,oBACEnC,OAAA;MAAKmB,SAAS,EAAC,oGAAoG;MAAAjB,QAAA,eACjHF,OAAA;QAAKmB,SAAS,EAAC,sBAAsB;QAAAjB,QAAA,eACnCF,OAAA;UAAKmB,SAAS,EAAC,gDAAgD;UAAAjB,QAAA,gBAC7DF,OAAA;YAAKmB,SAAS,EAAC,iFAAiF;YAAAjB,QAAA,eAC9FF,OAAA;cAAKmB,SAAS,EAAC,wBAAwB;cAACK,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAxB,QAAA,eAC3FF,OAAA;gBAAM2B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAoH;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvB,OAAA;YAAImB,SAAS,EAAC,uCAAuC;YAAAjB,QAAA,EAAC;UAAa;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEvB,OAAA;YAAKmB,SAAS,EAAC,MAAM;YAAAjB,QAAA,gBACnBF,OAAA;cAAGmB,SAAS,EAAC,oBAAoB;cAAAjB,QAAA,EAAEgC,gBAAgB,CAAC;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DvB,OAAA;cAAKmB,SAAS,EAAC,gCAAgC;cAAAjB,QAAA,gBAC7CF,OAAA;gBAAGmB,SAAS,EAAC,uBAAuB;gBAAAjB,QAAA,gBAClCF,OAAA;kBAAAE,QAAA,EAAQ;gBAAU;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,eAAAvB,OAAA;kBAAMmB,SAAS,EAAC,0BAA0B;kBAAAjB,QAAA,EAAEQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,EACHnB,YAAY,CAACY,MAAM,GAAG,CAAC,iBACtBhB,OAAA;gBAAGmB,SAAS,EAAC,4BAA4B;gBAAAjB,QAAA,gBACvCF,OAAA;kBAAAE,QAAA,EAAQ;gBAAe;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,eAAAvB,OAAA;kBAAMmB,SAAS,EAAC,gBAAgB;kBAAAjB,QAAA,EAAEE,YAAY,CAAC+B,IAAI,CAAC,IAAI;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvB,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAjB,QAAA,gBACxBF,OAAA;cACE+B,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;cACrCnB,SAAS,EAAC,4KAA4K;cAAAjB,QAAA,EACvL;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvB,OAAA;cACE+B,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAACG,QAAQ,CAACC,IAAI,GAAG,GAAI;cAC1CrB,SAAS,EAAC,kJAAkJ;cAAAjB,QAAA,EAC7J;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,OAAOrB,QAAQ;AACjB;AAACG,EAAA,CA5JQJ,cAAc;EAAA,QACsCL,OAAO;AAAA;AAAA6C,EAAA,GAD3DxC,cAAc;AA8JvB,eAAeA,cAAc;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}