{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Healthcare\\\\frontend\\\\src\\\\components\\\\FlutterwavePayment.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FlutterwavePayment = ({\n  amount,\n  customerEmail,\n  customerPhone,\n  customerName,\n  billId,\n  onSuccess,\n  onClose\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');\n\n  // Load Flutterwave script\n  useEffect(() => {\n    const script = document.createElement('script');\n    script.src = 'https://checkout.flutterwave.com/v3.js';\n    script.async = true;\n    document.body.appendChild(script);\n    return () => {\n      // Cleanup script when component unmounts\n      if (document.body.contains(script)) {\n        document.body.removeChild(script);\n      }\n    };\n  }, []);\n  const initiatePayment = () => {\n    setLoading(true);\n\n    // Check if Flutterwave public key is configured\n    const publicKey = process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY;\n    if (!publicKey || publicKey === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X') {\n      // Demo mode - simulate payment for testing\n      const paymentMethodName = {\n        'card': 'Credit/Debit Card',\n        'mtn': 'MTN Mobile Money',\n        'bank': 'Bank Transfer',\n        'ussd': 'USSD Payment'\n      }[selectedPaymentMethod] || 'Card Payment';\n      setTimeout(() => {\n        const confirmed = window.confirm(`Demo Payment Mode\\n\\nAmount: ${amount.toLocaleString()} RWF\\nPatient: ${customerName}\\nPayment Method: ${paymentMethodName}\\n\\nClick OK to simulate successful payment, Cancel to simulate failed payment.`);\n        setLoading(false);\n        if (confirmed) {\n          // Simulate successful payment\n          onSuccess({\n            transactionId: `DEMO_${Date.now()}`,\n            flwRef: `FLW_DEMO_${billId}`,\n            amount: amount,\n            currency: 'RWF',\n            status: 'successful',\n            paymentType: selectedPaymentMethod,\n            chargedAmount: amount\n          });\n        } else {\n          alert('Demo payment cancelled.');\n          onClose();\n        }\n      }, 1000);\n      return;\n    }\n\n    // Check if Flutterwave script is loaded\n    if (typeof window.FlutterwaveCheckout === 'undefined') {\n      alert('Payment system is loading. Please try again in a moment.');\n      setLoading(false);\n      return;\n    }\n\n    // Get payment options based on selected method\n    const getPaymentOptions = () => {\n      switch (selectedPaymentMethod) {\n        case 'mtn':\n          return 'mobilemoney';\n        case 'card':\n          return 'card';\n        case 'bank':\n          return 'banktransfer';\n        case 'ussd':\n          return 'ussd';\n        default:\n          return 'card,mobilemoney,banktransfer,ussd';\n      }\n    };\n\n    // Flutterwave payment configuration\n    const paymentData = {\n      public_key: publicKey,\n      tx_ref: `HEALTHCARE_${billId}_${Date.now()}`,\n      amount: amount,\n      currency: 'RWF',\n      payment_options: getPaymentOptions(),\n      customer: {\n        email: customerEmail,\n        phone_number: customerPhone,\n        name: customerName\n      },\n      customizations: {\n        title: 'Healthcare Payment',\n        description: `Payment for medical bill #${billId}`,\n        logo: '/logo.png'\n      },\n      callback: response => {\n        console.log('Flutterwave payment response:', response);\n        setLoading(false);\n        if (response.status === 'successful') {\n          // Payment successful\n          onSuccess({\n            transactionId: response.transaction_id,\n            flwRef: response.flw_ref,\n            amount: response.amount,\n            currency: response.currency,\n            status: response.status,\n            paymentType: response.payment_type,\n            chargedAmount: response.charged_amount\n          });\n        } else {\n          // Payment failed or cancelled\n          alert('Payment was not successful. Please try again.');\n        }\n      },\n      onclose: () => {\n        console.log('Payment modal closed');\n        setLoading(false);\n        onClose();\n      }\n    };\n\n    // Initialize Flutterwave payment\n    window.FlutterwaveCheckout(paymentData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 relative max-h-[90vh] overflow-y-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-lg hover:bg-gray-100\",\n        title: \"Close Payment Modal\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-6 h-6\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M6 18L18 6M6 6l12 12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-white\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Secure Payment with Flutterwave\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Complete your medical bill payment securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-xl p-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-gray-900 mb-3\",\n          children: \"Payment Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Patient:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: customerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Bill ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [\"#\", billId]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: customerEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Phone:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: customerPhone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t pt-2 mt-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-lg font-bold\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Amount to Pay:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600\",\n                children: [amount.toLocaleString(), \" RWF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-gray-900 mb-3\",\n          children: \"Select Payment Method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPaymentMethod('card'),\n            className: `p-3 rounded-lg border-2 text-center transition-all duration-200 ${selectedPaymentMethod === 'card' ? 'bg-blue-100 border-blue-500 shadow-md' : 'bg-blue-50 border-blue-200 hover:border-blue-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-600 font-medium text-sm\",\n              children: \"\\uD83D\\uDCB3 Card Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-600\",\n              children: \"Visa, Mastercard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 13\n            }, this), selectedPaymentMethod === 'card' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block w-2 h-2 bg-blue-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPaymentMethod('mtn'),\n            className: `p-3 rounded-lg border-2 text-center transition-all duration-200 ${selectedPaymentMethod === 'mtn' ? 'bg-yellow-100 border-yellow-500 shadow-md' : 'bg-yellow-50 border-yellow-200 hover:border-yellow-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-yellow-600 font-medium text-sm\",\n              children: \"\\uD83D\\uDCF1 MTN MoMo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-600\",\n              children: \"Mobile Money\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 13\n            }, this), selectedPaymentMethod === 'mtn' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block w-2 h-2 bg-yellow-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPaymentMethod('bank'),\n            className: `p-3 rounded-lg border-2 text-center transition-all duration-200 ${selectedPaymentMethod === 'bank' ? 'bg-purple-100 border-purple-500 shadow-md' : 'bg-purple-50 border-purple-200 hover:border-purple-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-600 font-medium text-sm\",\n              children: \"\\uD83C\\uDFE6 Bank Transfer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-600\",\n              children: \"Direct transfer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 13\n            }, this), selectedPaymentMethod === 'bank' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block w-2 h-2 bg-purple-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPaymentMethod('ussd'),\n            className: `p-3 rounded-lg border-2 text-center transition-all duration-200 ${selectedPaymentMethod === 'ussd' ? 'bg-orange-100 border-orange-500 shadow-md' : 'bg-orange-50 border-orange-200 hover:border-orange-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-orange-600 font-medium text-sm\",\n              children: \"\\uD83D\\uDCDE USSD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-600\",\n              children: \"Dial code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 13\n            }, this), selectedPaymentMethod === 'ussd' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block w-2 h-2 bg-orange-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-3 bg-gray-50 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Selected: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 13\n            }, this), selectedPaymentMethod === 'card' && 'Credit/Debit Card Payment', selectedPaymentMethod === 'mtn' && 'MTN Mobile Money', selectedPaymentMethod === 'bank' && 'Bank Transfer', selectedPaymentMethod === 'ussd' && 'USSD Payment']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `border rounded-lg p-3 mb-6 ${!process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X' ? 'bg-yellow-50 border-yellow-200' : 'bg-blue-50 border-blue-200'}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: `w-5 h-5 mr-2 mt-0.5 ${!process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X' ? 'text-yellow-500' : 'text-blue-500'}`,\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: !process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-medium text-yellow-900 text-sm\",\n                children: \"Demo Mode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-yellow-700 mt-1\",\n                children: \"This is demo mode. Configure your Flutterwave API key in the .env file for live payments.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-medium text-blue-900 text-sm\",\n                children: \"Secure Payment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-700 mt-1\",\n                children: \"Your payment is secured by Flutterwave's industry-standard encryption and security measures.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-colors\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: initiatePayment,\n          disabled: loading,\n          className: \"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), \"Processing...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), !process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X' ? `Demo Payment (${selectedPaymentMethod === 'card' ? 'Card' : selectedPaymentMethod === 'mtn' ? 'MTN' : selectedPaymentMethod === 'bank' ? 'Bank' : 'USSD'})` : `Pay with ${selectedPaymentMethod === 'card' ? 'Card' : selectedPaymentMethod === 'mtn' ? 'MTN MoMo' : selectedPaymentMethod === 'bank' ? 'Bank' : 'USSD'}`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(FlutterwavePayment, \"UNeDjLNwHoRySU1UPEDyDVdepZI=\");\n_c = FlutterwavePayment;\nexport default FlutterwavePayment;\nvar _c;\n$RefreshReg$(_c, \"FlutterwavePayment\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FlutterwavePayment", "amount", "customerEmail", "customerPhone", "customerName", "billId", "onSuccess", "onClose", "_s", "loading", "setLoading", "selectedPaymentMethod", "setSelectedPaymentMethod", "script", "document", "createElement", "src", "async", "body", "append<PERSON><PERSON><PERSON>", "contains", "<PERSON><PERSON><PERSON><PERSON>", "initiatePayment", "public<PERSON>ey", "process", "env", "REACT_APP_FLUTTERWAVE_PUBLIC_KEY", "paymentMethodName", "setTimeout", "confirmed", "window", "confirm", "toLocaleString", "transactionId", "Date", "now", "flwRef", "currency", "status", "paymentType", "chargedAmount", "alert", "FlutterwaveCheckout", "getPaymentOptions", "paymentData", "public_key", "tx_ref", "payment_options", "customer", "email", "phone_number", "name", "customizations", "title", "description", "logo", "callback", "response", "console", "log", "transaction_id", "flw_ref", "payment_type", "charged_amount", "onclose", "className", "children", "onClick", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Healthcare/frontend/src/components/FlutterwavePayment.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nconst FlutterwavePayment = ({\n  amount,\n  customerEmail,\n  customerPhone,\n  customerName,\n  billId,\n  onSuccess,\n  onClose\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');\n\n  // Load Flutterwave script\n  useEffect(() => {\n    const script = document.createElement('script');\n    script.src = 'https://checkout.flutterwave.com/v3.js';\n    script.async = true;\n    document.body.appendChild(script);\n\n    return () => {\n      // Cleanup script when component unmounts\n      if (document.body.contains(script)) {\n        document.body.removeChild(script);\n      }\n    };\n  }, []);\n\n  const initiatePayment = () => {\n    setLoading(true);\n\n    // Check if Flutterwave public key is configured\n    const publicKey = process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY;\n\n    if (!publicKey || publicKey === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X') {\n      // Demo mode - simulate payment for testing\n      const paymentMethodName = {\n        'card': 'Credit/Debit Card',\n        'mtn': 'MTN Mobile Money',\n        'bank': 'Bank Transfer',\n        'ussd': 'USSD Payment'\n      }[selectedPaymentMethod] || 'Card Payment';\n\n      setTimeout(() => {\n        const confirmed = window.confirm(\n          `Demo Payment Mode\\n\\nAmount: ${amount.toLocaleString()} RWF\\nPatient: ${customerName}\\nPayment Method: ${paymentMethodName}\\n\\nClick OK to simulate successful payment, Cancel to simulate failed payment.`\n        );\n\n        setLoading(false);\n\n        if (confirmed) {\n          // Simulate successful payment\n          onSuccess({\n            transactionId: `DEMO_${Date.now()}`,\n            flwRef: `FLW_DEMO_${billId}`,\n            amount: amount,\n            currency: 'RWF',\n            status: 'successful',\n            paymentType: selectedPaymentMethod,\n            chargedAmount: amount\n          });\n        } else {\n          alert('Demo payment cancelled.');\n          onClose();\n        }\n      }, 1000);\n      return;\n    }\n\n    // Check if Flutterwave script is loaded\n    if (typeof window.FlutterwaveCheckout === 'undefined') {\n      alert('Payment system is loading. Please try again in a moment.');\n      setLoading(false);\n      return;\n    }\n\n    // Get payment options based on selected method\n    const getPaymentOptions = () => {\n      switch (selectedPaymentMethod) {\n        case 'mtn':\n          return 'mobilemoney';\n        case 'card':\n          return 'card';\n        case 'bank':\n          return 'banktransfer';\n        case 'ussd':\n          return 'ussd';\n        default:\n          return 'card,mobilemoney,banktransfer,ussd';\n      }\n    };\n\n    // Flutterwave payment configuration\n    const paymentData = {\n      public_key: publicKey,\n      tx_ref: `HEALTHCARE_${billId}_${Date.now()}`,\n      amount: amount,\n      currency: 'RWF',\n      payment_options: getPaymentOptions(),\n      customer: {\n        email: customerEmail,\n        phone_number: customerPhone,\n        name: customerName,\n      },\n      customizations: {\n        title: 'Healthcare Payment',\n        description: `Payment for medical bill #${billId}`,\n        logo: '/logo.png',\n      },\n      callback: (response) => {\n        console.log('Flutterwave payment response:', response);\n        setLoading(false);\n\n        if (response.status === 'successful') {\n          // Payment successful\n          onSuccess({\n            transactionId: response.transaction_id,\n            flwRef: response.flw_ref,\n            amount: response.amount,\n            currency: response.currency,\n            status: response.status,\n            paymentType: response.payment_type,\n            chargedAmount: response.charged_amount\n          });\n        } else {\n          // Payment failed or cancelled\n          alert('Payment was not successful. Please try again.');\n        }\n      },\n      onclose: () => {\n        console.log('Payment modal closed');\n        setLoading(false);\n        onClose();\n      },\n    };\n\n    // Initialize Flutterwave payment\n    window.FlutterwaveCheckout(paymentData);\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 relative max-h-[90vh] overflow-y-auto\">\n      <div className=\"p-6\">\n      {/* Close Button in Header */}\n      <button\n        onClick={onClose}\n        className=\"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-lg hover:bg-gray-100\"\n        title=\"Close Payment Modal\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n        </svg>\n      </button>\n      <div className=\"text-center mb-6\">\n        <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Secure Payment with Flutterwave</h3>\n        <p className=\"text-gray-600\">Complete your medical bill payment securely</p>\n      </div>\n\n      {/* Payment Details */}\n      <div className=\"bg-gray-50 rounded-xl p-4 mb-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-3\">Payment Details</h4>\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Patient:</span>\n            <span className=\"font-medium\">{customerName}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Bill ID:</span>\n            <span className=\"font-medium\">#{billId}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Email:</span>\n            <span className=\"font-medium\">{customerEmail}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Phone:</span>\n            <span className=\"font-medium\">{customerPhone}</span>\n          </div>\n          <div className=\"border-t pt-2 mt-3\">\n            <div className=\"flex justify-between text-lg font-bold\">\n              <span>Amount to Pay:</span>\n              <span className=\"text-green-600\">{amount.toLocaleString()} RWF</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Payment Method Selection */}\n      <div className=\"mb-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-3\">Select Payment Method</h4>\n        <div className=\"grid grid-cols-2 gap-3\">\n          {/* Card Payment */}\n          <button\n            onClick={() => setSelectedPaymentMethod('card')}\n            className={`p-3 rounded-lg border-2 text-center transition-all duration-200 ${\n              selectedPaymentMethod === 'card'\n                ? 'bg-blue-100 border-blue-500 shadow-md'\n                : 'bg-blue-50 border-blue-200 hover:border-blue-300'\n            }`}\n          >\n            <div className=\"text-blue-600 font-medium text-sm\">💳 Card Payment</div>\n            <div className=\"text-xs text-gray-600\">Visa, Mastercard</div>\n            {selectedPaymentMethod === 'card' && (\n              <div className=\"mt-1\">\n                <span className=\"inline-block w-2 h-2 bg-blue-500 rounded-full\"></span>\n              </div>\n            )}\n          </button>\n\n          {/* MTN Mobile Money */}\n          <button\n            onClick={() => setSelectedPaymentMethod('mtn')}\n            className={`p-3 rounded-lg border-2 text-center transition-all duration-200 ${\n              selectedPaymentMethod === 'mtn'\n                ? 'bg-yellow-100 border-yellow-500 shadow-md'\n                : 'bg-yellow-50 border-yellow-200 hover:border-yellow-300'\n            }`}\n          >\n            <div className=\"text-yellow-600 font-medium text-sm\">📱 MTN MoMo</div>\n            <div className=\"text-xs text-gray-600\">Mobile Money</div>\n            {selectedPaymentMethod === 'mtn' && (\n              <div className=\"mt-1\">\n                <span className=\"inline-block w-2 h-2 bg-yellow-500 rounded-full\"></span>\n              </div>\n            )}\n          </button>\n\n          {/* Bank Transfer */}\n          <button\n            onClick={() => setSelectedPaymentMethod('bank')}\n            className={`p-3 rounded-lg border-2 text-center transition-all duration-200 ${\n              selectedPaymentMethod === 'bank'\n                ? 'bg-purple-100 border-purple-500 shadow-md'\n                : 'bg-purple-50 border-purple-200 hover:border-purple-300'\n            }`}\n          >\n            <div className=\"text-purple-600 font-medium text-sm\">🏦 Bank Transfer</div>\n            <div className=\"text-xs text-gray-600\">Direct transfer</div>\n            {selectedPaymentMethod === 'bank' && (\n              <div className=\"mt-1\">\n                <span className=\"inline-block w-2 h-2 bg-purple-500 rounded-full\"></span>\n              </div>\n            )}\n          </button>\n\n          {/* USSD */}\n          <button\n            onClick={() => setSelectedPaymentMethod('ussd')}\n            className={`p-3 rounded-lg border-2 text-center transition-all duration-200 ${\n              selectedPaymentMethod === 'ussd'\n                ? 'bg-orange-100 border-orange-500 shadow-md'\n                : 'bg-orange-50 border-orange-200 hover:border-orange-300'\n            }`}\n          >\n            <div className=\"text-orange-600 font-medium text-sm\">📞 USSD</div>\n            <div className=\"text-xs text-gray-600\">Dial code</div>\n            {selectedPaymentMethod === 'ussd' && (\n              <div className=\"mt-1\">\n                <span className=\"inline-block w-2 h-2 bg-orange-500 rounded-full\"></span>\n              </div>\n            )}\n          </button>\n        </div>\n\n        {/* Selected Payment Method Info */}\n        <div className=\"mt-3 p-3 bg-gray-50 rounded-lg\">\n          <div className=\"text-sm text-gray-700\">\n            <span className=\"font-medium\">Selected: </span>\n            {selectedPaymentMethod === 'card' && 'Credit/Debit Card Payment'}\n            {selectedPaymentMethod === 'mtn' && 'MTN Mobile Money'}\n            {selectedPaymentMethod === 'bank' && 'Bank Transfer'}\n            {selectedPaymentMethod === 'ussd' && 'USSD Payment'}\n          </div>\n        </div>\n      </div>\n\n      {/* Security Notice */}\n      <div className={`border rounded-lg p-3 mb-6 ${\n        !process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X'\n          ? 'bg-yellow-50 border-yellow-200'\n          : 'bg-blue-50 border-blue-200'\n      }`}>\n        <div className=\"flex items-start\">\n          <svg className={`w-5 h-5 mr-2 mt-0.5 ${\n            !process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X'\n              ? 'text-yellow-500'\n              : 'text-blue-500'\n          }`} fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n          </svg>\n          <div>\n            {!process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X' ? (\n              <>\n                <h5 className=\"font-medium text-yellow-900 text-sm\">Demo Mode</h5>\n                <p className=\"text-xs text-yellow-700 mt-1\">\n                  This is demo mode. Configure your Flutterwave API key in the .env file for live payments.\n                </p>\n              </>\n            ) : (\n              <>\n                <h5 className=\"font-medium text-blue-900 text-sm\">Secure Payment</h5>\n                <p className=\"text-xs text-blue-700 mt-1\">\n                  Your payment is secured by Flutterwave's industry-standard encryption and security measures.\n                </p>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex gap-3\">\n        <button\n          onClick={onClose}\n          className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-colors\"\n        >\n          Cancel\n        </button>\n        <button\n          onClick={initiatePayment}\n          disabled={loading}\n          className=\"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50\"\n        >\n          {loading ? (\n            <div className=\"flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"></div>\n              Processing...\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-center\">\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n              </svg>\n              {!process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY === 'FLWPUBK_TEST-SANDBOXDEMOKEY-X'\n                ? `Demo Payment (${selectedPaymentMethod === 'card' ? 'Card' : selectedPaymentMethod === 'mtn' ? 'MTN' : selectedPaymentMethod === 'bank' ? 'Bank' : 'USSD'})`\n                : `Pay with ${selectedPaymentMethod === 'card' ? 'Card' : selectedPaymentMethod === 'mtn' ? 'MTN MoMo' : selectedPaymentMethod === 'bank' ? 'Bank' : 'USSD'}`\n              }\n            </div>\n          )}\n        </button>\n      </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FlutterwavePayment;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,aAAa;EACbC,aAAa;EACbC,YAAY;EACZC,MAAM;EACNC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlB,QAAQ,CAAC,MAAM,CAAC;;EAE1E;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,GAAG,GAAG,wCAAwC;IACrDH,MAAM,CAACI,KAAK,GAAG,IAAI;IACnBH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;IAEjC,OAAO,MAAM;MACX;MACA,IAAIC,QAAQ,CAACI,IAAI,CAACE,QAAQ,CAACP,MAAM,CAAC,EAAE;QAClCC,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,MAAM,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5BZ,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAMa,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,gCAAgC;IAE9D,IAAI,CAACH,SAAS,IAAIA,SAAS,KAAK,+BAA+B,EAAE;MAC/D;MACA,MAAMI,iBAAiB,GAAG;QACxB,MAAM,EAAE,mBAAmB;QAC3B,KAAK,EAAE,kBAAkB;QACzB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE;MACV,CAAC,CAAChB,qBAAqB,CAAC,IAAI,cAAc;MAE1CiB,UAAU,CAAC,MAAM;QACf,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAC9B,gCAAgC9B,MAAM,CAAC+B,cAAc,CAAC,CAAC,kBAAkB5B,YAAY,qBAAqBuB,iBAAiB,iFAC7H,CAAC;QAEDjB,UAAU,CAAC,KAAK,CAAC;QAEjB,IAAImB,SAAS,EAAE;UACb;UACAvB,SAAS,CAAC;YACR2B,aAAa,EAAE,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YACnCC,MAAM,EAAE,YAAY/B,MAAM,EAAE;YAC5BJ,MAAM,EAAEA,MAAM;YACdoC,QAAQ,EAAE,KAAK;YACfC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE5B,qBAAqB;YAClC6B,aAAa,EAAEvC;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLwC,KAAK,CAAC,yBAAyB,CAAC;UAChClC,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,IAAI,CAAC;MACR;IACF;;IAEA;IACA,IAAI,OAAOuB,MAAM,CAACY,mBAAmB,KAAK,WAAW,EAAE;MACrDD,KAAK,CAAC,0DAA0D,CAAC;MACjE/B,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,MAAMiC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,QAAQhC,qBAAqB;QAC3B,KAAK,KAAK;UACR,OAAO,aAAa;QACtB,KAAK,MAAM;UACT,OAAO,MAAM;QACf,KAAK,MAAM;UACT,OAAO,cAAc;QACvB,KAAK,MAAM;UACT,OAAO,MAAM;QACf;UACE,OAAO,oCAAoC;MAC/C;IACF,CAAC;;IAED;IACA,MAAMiC,WAAW,GAAG;MAClBC,UAAU,EAAEtB,SAAS;MACrBuB,MAAM,EAAE,cAAczC,MAAM,IAAI6B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC5ClC,MAAM,EAAEA,MAAM;MACdoC,QAAQ,EAAE,KAAK;MACfU,eAAe,EAAEJ,iBAAiB,CAAC,CAAC;MACpCK,QAAQ,EAAE;QACRC,KAAK,EAAE/C,aAAa;QACpBgD,YAAY,EAAE/C,aAAa;QAC3BgD,IAAI,EAAE/C;MACR,CAAC;MACDgD,cAAc,EAAE;QACdC,KAAK,EAAE,oBAAoB;QAC3BC,WAAW,EAAE,6BAA6BjD,MAAM,EAAE;QAClDkD,IAAI,EAAE;MACR,CAAC;MACDC,QAAQ,EAAGC,QAAQ,IAAK;QACtBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,QAAQ,CAAC;QACtD/C,UAAU,CAAC,KAAK,CAAC;QAEjB,IAAI+C,QAAQ,CAACnB,MAAM,KAAK,YAAY,EAAE;UACpC;UACAhC,SAAS,CAAC;YACR2B,aAAa,EAAEwB,QAAQ,CAACG,cAAc;YACtCxB,MAAM,EAAEqB,QAAQ,CAACI,OAAO;YACxB5D,MAAM,EAAEwD,QAAQ,CAACxD,MAAM;YACvBoC,QAAQ,EAAEoB,QAAQ,CAACpB,QAAQ;YAC3BC,MAAM,EAAEmB,QAAQ,CAACnB,MAAM;YACvBC,WAAW,EAAEkB,QAAQ,CAACK,YAAY;YAClCtB,aAAa,EAAEiB,QAAQ,CAACM;UAC1B,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAtB,KAAK,CAAC,+CAA+C,CAAC;QACxD;MACF,CAAC;MACDuB,OAAO,EAAEA,CAAA,KAAM;QACbN,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnCjD,UAAU,CAAC,KAAK,CAAC;QACjBH,OAAO,CAAC,CAAC;MACX;IACF,CAAC;;IAED;IACAuB,MAAM,CAACY,mBAAmB,CAACE,WAAW,CAAC;EACzC,CAAC;EAED,oBACE/C,OAAA;IAAKoE,SAAS,EAAC,6FAA6F;IAAAC,QAAA,eAC1GrE,OAAA;MAAKoE,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAEpBrE,OAAA;QACEsE,OAAO,EAAE5D,OAAQ;QACjB0D,SAAS,EAAC,6GAA6G;QACvHZ,KAAK,EAAC,qBAAqB;QAAAa,QAAA,eAE3BrE,OAAA;UAAKoE,SAAS,EAAC,SAAS;UAACG,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,OAAO,EAAC,WAAW;UAAAL,QAAA,eAC5FrE,OAAA;YAAM2E,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACTjF,OAAA;QAAKoE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrE,OAAA;UAAKoE,SAAS,EAAC,mHAAmH;UAAAC,QAAA,eAChIrE,OAAA;YAAKoE,SAAS,EAAC,oBAAoB;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAL,QAAA,eACvGrE,OAAA;cAAM2E,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAwF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7I;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjF,OAAA;UAAIoE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAA+B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzFjF,OAAA;UAAGoE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2C;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAGNjF,OAAA;QAAKoE,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrE,OAAA;UAAIoE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEjF,OAAA;UAAKoE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrE,OAAA;YAAKoE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CjF,OAAA;cAAMoE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAE9D;YAAY;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNjF,OAAA;YAAKoE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CjF,OAAA;cAAMoE,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,GAAC,EAAC7D,MAAM;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNjF,OAAA;YAAKoE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CjF,OAAA;cAAMoE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEhE;YAAa;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNjF,OAAA;YAAKoE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CjF,OAAA;cAAMoE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAE/D;YAAa;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNjF,OAAA;YAAKoE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCrE,OAAA;cAAKoE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDrE,OAAA;gBAAAqE,QAAA,EAAM;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BjF,OAAA;gBAAMoE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAEjE,MAAM,CAAC+B,cAAc,CAAC,CAAC,EAAC,MAAI;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjF,OAAA;QAAKoE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrE,OAAA;UAAIoE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EjF,OAAA;UAAKoE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCrE,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,MAAM,CAAE;YAChDqD,SAAS,EAAE,mEACTtD,qBAAqB,KAAK,MAAM,GAC5B,uCAAuC,GACvC,kDAAkD,EACrD;YAAAuD,QAAA,gBAEHrE,OAAA;cAAKoE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxEjF,OAAA;cAAKoE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC5DnE,qBAAqB,KAAK,MAAM,iBAC/Bd,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBrE,OAAA;gBAAMoE,SAAS,EAAC;cAA+C;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAGTjF,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,KAAK,CAAE;YAC/CqD,SAAS,EAAE,mEACTtD,qBAAqB,KAAK,KAAK,GAC3B,2CAA2C,GAC3C,wDAAwD,EAC3D;YAAAuD,QAAA,gBAEHrE,OAAA;cAAKoE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEjF,OAAA;cAAKoE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACxDnE,qBAAqB,KAAK,KAAK,iBAC9Bd,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBrE,OAAA;gBAAMoE,SAAS,EAAC;cAAiD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAGTjF,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,MAAM,CAAE;YAChDqD,SAAS,EAAE,mEACTtD,qBAAqB,KAAK,MAAM,GAC5B,2CAA2C,GAC3C,wDAAwD,EAC3D;YAAAuD,QAAA,gBAEHrE,OAAA;cAAKoE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3EjF,OAAA;cAAKoE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC3DnE,qBAAqB,KAAK,MAAM,iBAC/Bd,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBrE,OAAA;gBAAMoE,SAAS,EAAC;cAAiD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAGTjF,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,MAAM,CAAE;YAChDqD,SAAS,EAAE,mEACTtD,qBAAqB,KAAK,MAAM,GAC5B,2CAA2C,GAC3C,wDAAwD,EAC3D;YAAAuD,QAAA,gBAEHrE,OAAA;cAAKoE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClEjF,OAAA;cAAKoE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACrDnE,qBAAqB,KAAK,MAAM,iBAC/Bd,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBrE,OAAA;gBAAMoE,SAAS,EAAC;cAAiD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNjF,OAAA;UAAKoE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7CrE,OAAA;YAAKoE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCrE,OAAA;cAAMoE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC9CnE,qBAAqB,KAAK,MAAM,IAAI,2BAA2B,EAC/DA,qBAAqB,KAAK,KAAK,IAAI,kBAAkB,EACrDA,qBAAqB,KAAK,MAAM,IAAI,eAAe,EACnDA,qBAAqB,KAAK,MAAM,IAAI,cAAc;UAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjF,OAAA;QAAKoE,SAAS,EAAE,8BACd,CAACzC,OAAO,CAACC,GAAG,CAACC,gCAAgC,IAAIF,OAAO,CAACC,GAAG,CAACC,gCAAgC,KAAK,+BAA+B,GAC7H,gCAAgC,GAChC,4BAA4B,EAC/B;QAAAwC,QAAA,eACDrE,OAAA;UAAKoE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrE,OAAA;YAAKoE,SAAS,EAAE,uBACd,CAACzC,OAAO,CAACC,GAAG,CAACC,gCAAgC,IAAIF,OAAO,CAACC,GAAG,CAACC,gCAAgC,KAAK,+BAA+B,GAC7H,iBAAiB,GACjB,eAAe,EAClB;YAAC0C,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAL,QAAA,eACvErE,OAAA;cAAM2E,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3J,CAAC,eACNjF,OAAA;YAAAqE,QAAA,EACG,CAAC1C,OAAO,CAACC,GAAG,CAACC,gCAAgC,IAAIF,OAAO,CAACC,GAAG,CAACC,gCAAgC,KAAK,+BAA+B,gBAChI7B,OAAA,CAAAE,SAAA;cAAAmE,QAAA,gBACErE,OAAA;gBAAIoE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEjF,OAAA;gBAAGoE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eACJ,CAAC,gBAEHjF,OAAA,CAAAE,SAAA;cAAAmE,QAAA,gBACErE,OAAA;gBAAIoE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEjF,OAAA;gBAAGoE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eACJ;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjF,OAAA;QAAKoE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBrE,OAAA;UACEsE,OAAO,EAAE5D,OAAQ;UACjB0D,SAAS,EAAC,yGAAyG;UAAAC,QAAA,EACpH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjF,OAAA;UACEsE,OAAO,EAAE7C,eAAgB;UACzByD,QAAQ,EAAEtE,OAAQ;UAClBwD,SAAS,EAAC,yOAAyO;UAAAC,QAAA,EAElPzD,OAAO,gBACNZ,OAAA;YAAKoE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CrE,OAAA;cAAKoE,SAAS,EAAC;YAAmF;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAE3G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAENjF,OAAA;YAAKoE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CrE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAL,QAAA,eACjGrE,OAAA;gBAAM2E,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAwF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC,EACL,CAACtD,OAAO,CAACC,GAAG,CAACC,gCAAgC,IAAIF,OAAO,CAACC,GAAG,CAACC,gCAAgC,KAAK,+BAA+B,GAC9H,iBAAiBf,qBAAqB,KAAK,MAAM,GAAG,MAAM,GAAGA,qBAAqB,KAAK,KAAK,GAAG,KAAK,GAAGA,qBAAqB,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,GAC5J,YAAYA,qBAAqB,KAAK,MAAM,GAAG,MAAM,GAAGA,qBAAqB,KAAK,KAAK,GAAG,UAAU,GAAGA,qBAAqB,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE;UAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5J;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA5VIR,kBAAkB;AAAAgF,EAAA,GAAlBhF,kBAAkB;AA8VxB,eAAeA,kBAAkB;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}