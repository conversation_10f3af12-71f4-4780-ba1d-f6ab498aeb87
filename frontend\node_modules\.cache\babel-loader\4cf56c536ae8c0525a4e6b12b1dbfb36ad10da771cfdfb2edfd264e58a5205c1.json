{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\Messages.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Messages() {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState('inbox');\n  const [selectedMessage, setSelectedMessage] = useState(null);\n  const [showCompose, setShowCompose] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n\n  // Compose form state\n  const [composeForm, setComposeForm] = useState({\n    receiverId: '',\n    subject: '',\n    content: '',\n    messageType: 'general',\n    priority: 'normal',\n    patientId: ''\n  });\n\n  // Fetch data on component mount\n  useEffect(() => {\n    if (user) {\n      fetchMessages();\n      fetchUsers();\n      fetchUnreadCount();\n    }\n  }, [user, activeTab]);\n\n  // Fetch messages based on active tab\n  const fetchMessages = async () => {\n    if (!user) return;\n    setLoading(true);\n    try {\n      let endpoint = '';\n      switch (activeTab) {\n        case 'inbox':\n          endpoint = `http://localhost:5000/api/messages/inbox/${user.id}`;\n          break;\n        case 'sent':\n          endpoint = `http://localhost:5000/api/messages/sent/${user.id}`;\n          break;\n        case 'starred':\n          endpoint = `http://localhost:5000/api/messages/starred/${user.id}`;\n          break;\n        default:\n          endpoint = `http://localhost:5000/api/messages/inbox/${user.id}`;\n      }\n      const response = await fetch(endpoint);\n      const data = await response.json();\n      if (data.success) {\n        setMessages(data.data);\n      } else {\n        setError('Failed to fetch messages');\n      }\n    } catch (err) {\n      setError('Error connecting to server');\n      console.error('Error fetching messages:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch users for compose dropdown\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/users');\n      const data = await response.json();\n      if (data.success) {\n        // Filter out current user\n        setUsers(data.data.filter(u => u.id !== user.id));\n      }\n    } catch (err) {\n      console.error('Error fetching users:', err);\n    }\n  };\n\n  // Fetch unread count\n  const fetchUnreadCount = async () => {\n    if (!user) return;\n    try {\n      const response = await fetch(`http://localhost:5000/api/messages/unread-count/${user.id}`);\n      const data = await response.json();\n      if (data.success) {\n        setUnreadCount(data.count);\n      }\n    } catch (err) {\n      console.error('Error fetching unread count:', err);\n    }\n  };\n\n  // Handle compose form changes\n  const handleComposeChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setComposeForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Send message\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const response = await fetch('http://localhost:5000/api/messages', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          ...composeForm,\n          senderId: user.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Message sent successfully!');\n        setShowCompose(false);\n        setComposeForm({\n          receiverId: '',\n          subject: '',\n          content: '',\n          messageType: 'general',\n          priority: 'normal',\n          patientId: ''\n        });\n        fetchMessages();\n        fetchUnreadCount();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (err) {\n      console.error('Error sending message:', err);\n      alert('Error sending message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mark message as read\n  const markAsRead = async messageId => {\n    try {\n      const response = await fetch(`http://localhost:5000/api/messages/${messageId}/read`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      if (response.ok) {\n        fetchMessages();\n        fetchUnreadCount();\n      }\n    } catch (err) {\n      console.error('Error marking message as read:', err);\n    }\n  };\n\n  // Toggle star message\n  const toggleStar = async (messageId, isStarred) => {\n    try {\n      const response = await fetch(`http://localhost:5000/api/messages/${messageId}/star`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          userId: user.id,\n          isStarred: !isStarred\n        })\n      });\n      if (response.ok) {\n        fetchMessages();\n      }\n    } catch (err) {\n      console.error('Error toggling star:', err);\n    }\n  };\n\n  // Get priority color\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'low':\n        return 'bg-green-100 text-green-800';\n      case 'normal':\n        return 'bg-blue-100 text-blue-800';\n      case 'high':\n        return 'bg-orange-100 text-orange-800';\n      case 'urgent':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  // Get message type icon\n  const getMessageTypeIcon = type => {\n    switch (type) {\n      case 'urgent':\n        return '🚨';\n      case 'patient_related':\n        return '👤';\n      case 'system':\n        return '⚙️';\n      default:\n        return '💬';\n    }\n  };\n\n  // Format date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return 'Today';\n    } else if (diffDays === 2) {\n      return 'Yesterday';\n    } else if (diffDays <= 7) {\n      return `${diffDays - 1} days ago`;\n    } else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900\",\n              children: \"Messages\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-1\",\n              children: \"Secure communication for healthcare professionals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: [\"\\uD83D\\uDCE7 \", unreadCount, \" Unread\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCompose(true),\n              className: \"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-2 px-6 rounded-lg transition-all transform hover:scale-105\",\n              children: \"\\u2709\\uFE0F Compose\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"-mb-px flex space-x-8\",\n            children: [{\n              id: 'inbox',\n              label: '📥 Inbox',\n              count: unreadCount\n            }, {\n              id: 'sent',\n              label: '📤 Sent',\n              count: 0\n            }, {\n              id: 'starred',\n              label: '⭐ Starred',\n              count: 0\n            }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n              children: [tab.label, \" \", tab.count > 0 && `(${tab.count})`]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-xl p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-red-500 mr-2\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: \"2\",\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-700 font-medium\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-gray-600\",\n          children: \"Loading messages...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 border-b border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900 capitalize\",\n                children: activeTab\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: [messages.length, \" messages\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-200\",\n              children: messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-8 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-400 text-2xl\",\n                    children: \"\\uD83D\\uDCED\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 mb-2\",\n                  children: \"No messages\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: [\"You don't have any messages in your \", activeTab, \".\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this) : messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 hover:bg-gray-50 cursor-pointer transition-colors ${!message.isRead && activeTab === 'inbox' ? 'bg-blue-50 border-l-4 border-blue-500' : ''}`,\n                onClick: () => {\n                  setSelectedMessage(message);\n                  if (!message.isRead && activeTab === 'inbox') {\n                    markAsRead(message.id);\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-lg\",\n                        children: getMessageTypeIcon(message.messageType)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(message.priority)}`,\n                        children: message.priority\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 31\n                      }, this), !message.isRead && activeTab === 'inbox' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-900\",\n                        children: activeTab === 'sent' ? `To: ${message.receiverFirstName} ${message.receiverLastName}` : `From: ${message.senderFirstName} ${message.senderLastName}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500 capitalize\",\n                        children: [\"(\", activeTab === 'sent' ? message.receiverRole : message.senderRole, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-gray-900 mb-1 truncate\",\n                      children: message.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-sm line-clamp-2\",\n                      children: message.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col items-end gap-2 ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: formatDate(message.sentAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: e => {\n                        e.stopPropagation();\n                        toggleStar(message.id, message.isStarred);\n                      },\n                      className: `text-lg hover:scale-110 transition-transform ${message.isStarred ? 'text-yellow-500' : 'text-gray-300 hover:text-yellow-500'}`,\n                      children: \"\\u2B50\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 25\n                }, this)\n              }, message.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: selectedMessage ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg border border-gray-200 p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-gray-900\",\n                children: \"Message Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedMessage(null),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Subject\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedMessage.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: activeTab === 'sent' ? 'To' : 'From'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: activeTab === 'sent' ? `${selectedMessage.receiverFirstName} ${selectedMessage.receiverLastName}` : `${selectedMessage.senderFirstName} ${selectedMessage.senderLastName}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 capitalize\",\n                  children: activeTab === 'sent' ? selectedMessage.receiverRole : selectedMessage.senderRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Content\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-4 mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 whitespace-pre-wrap\",\n                    children: selectedMessage.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4 text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCC5 \", formatDate(selectedMessage.sentAt)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs ${getPriorityColor(selectedMessage.priority)}`,\n                  children: selectedMessage.priority\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600 text-2xl\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-gray-900 mb-2\",\n                children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm mb-4\",\n                children: [\"Role: \", user === null || user === void 0 ? void 0 : user.role]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 text-sm\",\n                children: \"Select a message to view details or compose a new message to communicate with your team.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), showCompose && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"\\u2709\\uFE0F Compose Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCompose(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Recipient *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"receiverId\",\n                value: composeForm.receiverId,\n                onChange: handleComposeChange,\n                required: true,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select recipient...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this), users.map(u => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: u.id,\n                  children: [u.firstName, \" \", u.lastName, \" (\", u.role, \")\"]\n                }, u.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Priority\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"priority\",\n                value: composeForm.priority,\n                onChange: handleComposeChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"low\",\n                  children: \"Low\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"normal\",\n                  children: \"Normal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"High\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"urgent\",\n                  children: \"Urgent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Message Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"messageType\",\n                value: composeForm.messageType,\n                onChange: handleComposeChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"general\",\n                  children: \"\\uD83D\\uDCAC General\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"urgent\",\n                  children: \"\\uD83D\\uDEA8 Urgent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"patient_related\",\n                  children: \"\\uD83D\\uDC64 Patient Related\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"system\",\n                  children: \"\\u2699\\uFE0F System\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Patient ID (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"patientId\",\n                value: composeForm.patientId,\n                onChange: handleComposeChange,\n                placeholder: \"Enter patient ID if applicable\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Subject *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"subject\",\n              value: composeForm.subject,\n              onChange: handleComposeChange,\n              required: true,\n              placeholder: \"Enter message subject\",\n              className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Message Content *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"content\",\n              value: composeForm.content,\n              onChange: handleComposeChange,\n              required: true,\n              rows: 6,\n              placeholder: \"Type your message here...\",\n              className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end gap-4 pt-4 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowCompose(false),\n              className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg transition-all transform hover:scale-105 disabled:opacity-50\",\n              children: loading ? 'Sending...' : '📤 Send Message'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n}\n_s(Messages, \"tnqksu3OwtCG7RnezAFkKqPyI4Q=\", false, function () {\n  return [useAuth];\n});\n_c = Messages;\nexport default Messages;\nvar _c;\n$RefreshReg$(_c, \"Messages\");", "map": {"version": 3, "names": ["useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Messages", "_s", "user", "messages", "setMessages", "users", "setUsers", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "selectedMessage", "setSelectedMessage", "showCompose", "setShowCompose", "unreadCount", "setUnreadCount", "composeForm", "setComposeForm", "receiverId", "subject", "content", "messageType", "priority", "patientId", "fetchMessages", "fetchUsers", "fetchUnreadCount", "endpoint", "id", "response", "fetch", "data", "json", "success", "err", "console", "filter", "u", "count", "handleComposeChange", "e", "name", "value", "target", "prev", "handleSendMessage", "preventDefault", "method", "headers", "body", "JSON", "stringify", "senderId", "alert", "message", "mark<PERSON><PERSON><PERSON>", "messageId", "userId", "ok", "toggleStar", "isStarred", "getPriorityColor", "getMessageTypeIcon", "type", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleDateString", "month", "day", "year", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "label", "map", "tab", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "isRead", "receiverFirstName", "receiverLastName", "senderFirstName", "senderLastName", "receiverR<PERSON>", "senderRole", "sentAt", "stopPropagation", "role", "onSubmit", "onChange", "required", "firstName", "lastName", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Healthcare/frontend/src/pages/Messages.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\n\nfunction Messages() {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState('inbox');\n  const [selectedMessage, setSelectedMessage] = useState(null);\n  const [showCompose, setShowCompose] = useState(false);\n  const [unreadCount, setUnreadCount] = useState(0);\n\n  // Compose form state\n  const [composeForm, setComposeForm] = useState({\n    receiverId: '',\n    subject: '',\n    content: '',\n    messageType: 'general',\n    priority: 'normal',\n    patientId: ''\n  });\n\n  // Fetch data on component mount\n  useEffect(() => {\n    if (user) {\n      fetchMessages();\n      fetchUsers();\n      fetchUnreadCount();\n    }\n  }, [user, activeTab]);\n\n  // Fetch messages based on active tab\n  const fetchMessages = async () => {\n    if (!user) return;\n\n    setLoading(true);\n    try {\n      let endpoint = '';\n      switch (activeTab) {\n        case 'inbox':\n          endpoint = `http://localhost:5000/api/messages/inbox/${user.id}`;\n          break;\n        case 'sent':\n          endpoint = `http://localhost:5000/api/messages/sent/${user.id}`;\n          break;\n        case 'starred':\n          endpoint = `http://localhost:5000/api/messages/starred/${user.id}`;\n          break;\n        default:\n          endpoint = `http://localhost:5000/api/messages/inbox/${user.id}`;\n      }\n\n      const response = await fetch(endpoint);\n      const data = await response.json();\n\n      if (data.success) {\n        setMessages(data.data);\n      } else {\n        setError('Failed to fetch messages');\n      }\n    } catch (err) {\n      setError('Error connecting to server');\n      console.error('Error fetching messages:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch users for compose dropdown\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/api/users');\n      const data = await response.json();\n\n      if (data.success) {\n        // Filter out current user\n        setUsers(data.data.filter(u => u.id !== user.id));\n      }\n    } catch (err) {\n      console.error('Error fetching users:', err);\n    }\n  };\n\n  // Fetch unread count\n  const fetchUnreadCount = async () => {\n    if (!user) return;\n\n    try {\n      const response = await fetch(`http://localhost:5000/api/messages/unread-count/${user.id}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setUnreadCount(data.count);\n      }\n    } catch (err) {\n      console.error('Error fetching unread count:', err);\n    }\n  };\n\n  // Handle compose form changes\n  const handleComposeChange = (e) => {\n    const { name, value } = e.target;\n    setComposeForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Send message\n  const handleSendMessage = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await fetch('http://localhost:5000/api/messages', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...composeForm,\n          senderId: user.id\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        alert('Message sent successfully!');\n        setShowCompose(false);\n        setComposeForm({\n          receiverId: '',\n          subject: '',\n          content: '',\n          messageType: 'general',\n          priority: 'normal',\n          patientId: ''\n        });\n        fetchMessages();\n        fetchUnreadCount();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (err) {\n      console.error('Error sending message:', err);\n      alert('Error sending message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mark message as read\n  const markAsRead = async (messageId) => {\n    try {\n      const response = await fetch(`http://localhost:5000/api/messages/${messageId}/read`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ userId: user.id }),\n      });\n\n      if (response.ok) {\n        fetchMessages();\n        fetchUnreadCount();\n      }\n    } catch (err) {\n      console.error('Error marking message as read:', err);\n    }\n  };\n\n  // Toggle star message\n  const toggleStar = async (messageId, isStarred) => {\n    try {\n      const response = await fetch(`http://localhost:5000/api/messages/${messageId}/star`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          userId: user.id,\n          isStarred: !isStarred\n        }),\n      });\n\n      if (response.ok) {\n        fetchMessages();\n      }\n    } catch (err) {\n      console.error('Error toggling star:', err);\n    }\n  };\n\n  // Get priority color\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'low': return 'bg-green-100 text-green-800';\n      case 'normal': return 'bg-blue-100 text-blue-800';\n      case 'high': return 'bg-orange-100 text-orange-800';\n      case 'urgent': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  // Get message type icon\n  const getMessageTypeIcon = (type) => {\n    switch (type) {\n      case 'urgent': return '🚨';\n      case 'patient_related': return '👤';\n      case 'system': return '⚙️';\n      default: return '💬';\n    }\n  };\n\n  // Format date\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) {\n      return 'Today';\n    } else if (diffDays === 2) {\n      return 'Yesterday';\n    } else if (diffDays <= 7) {\n      return `${diffDays - 1} days ago`;\n    } else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Messages</h1>\n              <p className=\"text-gray-600 mt-1\">Secure communication for healthcare professionals</p>\n            </div>\n            <div className=\"flex items-center gap-4\">\n              <div className=\"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg\">\n                <span className=\"text-sm font-medium\">📧 {unreadCount} Unread</span>\n              </div>\n              <button\n                onClick={() => setShowCompose(true)}\n                className=\"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-2 px-6 rounded-lg transition-all transform hover:scale-105\"\n              >\n                ✉️ Compose\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 py-8\">\n        {/* Tab Navigation */}\n        <div className=\"mb-8\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex space-x-8\">\n              {[\n                { id: 'inbox', label: '📥 Inbox', count: unreadCount },\n                { id: 'sent', label: '📤 Sent', count: 0 },\n                { id: 'starred', label: '⭐ Starred', count: 0 }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  {tab.label} {tab.count > 0 && `(${tab.count})`}\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-xl p-4 mb-6\">\n            <div className=\"flex items-center\">\n              <svg className=\"w-5 h-5 text-red-500 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-red-700 font-medium\">{error}</span>\n            </div>\n          </div>\n        )}\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-2 text-gray-600\">Loading messages...</p>\n          </div>\n        )}\n\n        {/* Messages List */}\n        {!loading && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Message List */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-xl shadow-lg border border-gray-200\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <h2 className=\"text-xl font-bold text-gray-900 capitalize\">{activeTab}</h2>\n                  <p className=\"text-gray-600 text-sm\">{messages.length} messages</p>\n                </div>\n\n                <div className=\"divide-y divide-gray-200\">\n                  {messages.length === 0 ? (\n                    <div className=\"p-8 text-center\">\n                      <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                        <span className=\"text-gray-400 text-2xl\">📭</span>\n                      </div>\n                      <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No messages</h3>\n                      <p className=\"text-gray-600\">You don't have any messages in your {activeTab}.</p>\n                    </div>\n                  ) : (\n                    messages.map((message) => (\n                      <div\n                        key={message.id}\n                        className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${\n                          !message.isRead && activeTab === 'inbox' ? 'bg-blue-50 border-l-4 border-blue-500' : ''\n                        }`}\n                        onClick={() => {\n                          setSelectedMessage(message);\n                          if (!message.isRead && activeTab === 'inbox') {\n                            markAsRead(message.id);\n                          }\n                        }}\n                      >\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center gap-2 mb-2\">\n                              <span className=\"text-lg\">{getMessageTypeIcon(message.messageType)}</span>\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(message.priority)}`}>\n                                {message.priority}\n                              </span>\n                              {!message.isRead && activeTab === 'inbox' && (\n                                <span className=\"w-2 h-2 bg-blue-500 rounded-full\"></span>\n                              )}\n                            </div>\n\n                            <div className=\"flex items-center gap-2 mb-1\">\n                              <span className=\"font-medium text-gray-900\">\n                                {activeTab === 'sent'\n                                  ? `To: ${message.receiverFirstName} ${message.receiverLastName}`\n                                  : `From: ${message.senderFirstName} ${message.senderLastName}`\n                                }\n                              </span>\n                              <span className=\"text-sm text-gray-500 capitalize\">\n                                ({activeTab === 'sent' ? message.receiverRole : message.senderRole})\n                              </span>\n                            </div>\n\n                            <h3 className=\"font-semibold text-gray-900 mb-1 truncate\">{message.subject}</h3>\n                            <p className=\"text-gray-600 text-sm line-clamp-2\">{message.content}</p>\n                          </div>\n\n                          <div className=\"flex flex-col items-end gap-2 ml-4\">\n                            <span className=\"text-xs text-gray-500\">{formatDate(message.sentAt)}</span>\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleStar(message.id, message.isStarred);\n                              }}\n                              className={`text-lg hover:scale-110 transition-transform ${\n                                message.isStarred ? 'text-yellow-500' : 'text-gray-300 hover:text-yellow-500'\n                              }`}\n                            >\n                              ⭐\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Message Detail / User Info */}\n            <div className=\"lg:col-span-1\">\n              {selectedMessage ? (\n                <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-bold text-gray-900\">Message Details</h3>\n                    <button\n                      onClick={() => setSelectedMessage(null)}\n                      className=\"text-gray-400 hover:text-gray-600\"\n                    >\n                      ✕\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-700\">Subject</label>\n                      <p className=\"text-gray-900\">{selectedMessage.subject}</p>\n                    </div>\n\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-700\">\n                        {activeTab === 'sent' ? 'To' : 'From'}\n                      </label>\n                      <p className=\"text-gray-900\">\n                        {activeTab === 'sent'\n                          ? `${selectedMessage.receiverFirstName} ${selectedMessage.receiverLastName}`\n                          : `${selectedMessage.senderFirstName} ${selectedMessage.senderLastName}`\n                        }\n                      </p>\n                      <p className=\"text-sm text-gray-500 capitalize\">\n                        {activeTab === 'sent' ? selectedMessage.receiverRole : selectedMessage.senderRole}\n                      </p>\n                    </div>\n\n                    <div>\n                      <label className=\"text-sm font-medium text-gray-700\">Content</label>\n                      <div className=\"bg-gray-50 rounded-lg p-4 mt-1\">\n                        <p className=\"text-gray-900 whitespace-pre-wrap\">{selectedMessage.content}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                      <span>📅 {formatDate(selectedMessage.sentAt)}</span>\n                      <span className={`px-2 py-1 rounded-full text-xs ${getPriorityColor(selectedMessage.priority)}`}>\n                        {selectedMessage.priority}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <span className=\"text-blue-600 text-2xl\">👤</span>\n                    </div>\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Welcome, {user?.name}</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">Role: {user?.role}</p>\n                    <p className=\"text-gray-500 text-sm\">\n                      Select a message to view details or compose a new message to communicate with your team.\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Compose Modal */}\n      {showCompose && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-2xl font-bold text-gray-900\">✉️ Compose Message</h2>\n                <button\n                  onClick={() => setShowCompose(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            <form onSubmit={handleSendMessage} className=\"p-6 space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Recipient *\n                  </label>\n                  <select\n                    name=\"receiverId\"\n                    value={composeForm.receiverId}\n                    onChange={handleComposeChange}\n                    required\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">Select recipient...</option>\n                    {users.map((u) => (\n                      <option key={u.id} value={u.id}>\n                        {u.firstName} {u.lastName} ({u.role})\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Priority\n                  </label>\n                  <select\n                    name=\"priority\"\n                    value={composeForm.priority}\n                    onChange={handleComposeChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"low\">Low</option>\n                    <option value=\"normal\">Normal</option>\n                    <option value=\"high\">High</option>\n                    <option value=\"urgent\">Urgent</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Message Type\n                  </label>\n                  <select\n                    name=\"messageType\"\n                    value={composeForm.messageType}\n                    onChange={handleComposeChange}\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"general\">💬 General</option>\n                    <option value=\"urgent\">🚨 Urgent</option>\n                    <option value=\"patient_related\">👤 Patient Related</option>\n                    <option value=\"system\">⚙️ System</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Patient ID (Optional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"patientId\"\n                    value={composeForm.patientId}\n                    onChange={handleComposeChange}\n                    placeholder=\"Enter patient ID if applicable\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Subject *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"subject\"\n                  value={composeForm.subject}\n                  onChange={handleComposeChange}\n                  required\n                  placeholder=\"Enter message subject\"\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Message Content *\n                </label>\n                <textarea\n                  name=\"content\"\n                  value={composeForm.content}\n                  onChange={handleComposeChange}\n                  required\n                  rows={6}\n                  placeholder=\"Type your message here...\"\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                />\n              </div>\n\n              <div className=\"flex items-center justify-end gap-4 pt-4 border-t border-gray-200\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowCompose(false)}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg transition-all transform hover:scale-105 disabled:opacity-50\"\n                >\n                  {loading ? 'Sending...' : '📤 Send Message'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default Messages;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC;IAC7C0B,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,SAAS;IACtBC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIM,IAAI,EAAE;MACRyB,aAAa,CAAC,CAAC;MACfC,UAAU,CAAC,CAAC;MACZC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC3B,IAAI,EAAES,SAAS,CAAC,CAAC;;EAErB;EACA,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACzB,IAAI,EAAE;IAEXM,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIsB,QAAQ,GAAG,EAAE;MACjB,QAAQnB,SAAS;QACf,KAAK,OAAO;UACVmB,QAAQ,GAAG,4CAA4C5B,IAAI,CAAC6B,EAAE,EAAE;UAChE;QACF,KAAK,MAAM;UACTD,QAAQ,GAAG,2CAA2C5B,IAAI,CAAC6B,EAAE,EAAE;UAC/D;QACF,KAAK,SAAS;UACZD,QAAQ,GAAG,8CAA8C5B,IAAI,CAAC6B,EAAE,EAAE;UAClE;QACF;UACED,QAAQ,GAAG,4CAA4C5B,IAAI,CAAC6B,EAAE,EAAE;MACpE;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACH,QAAQ,CAAC;MACtC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBhC,WAAW,CAAC8B,IAAI,CAACA,IAAI,CAAC;MACxB,CAAC,MAAM;QACLxB,QAAQ,CAAC,0BAA0B,CAAC;MACtC;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ3B,QAAQ,CAAC,4BAA4B,CAAC;MACtC4B,OAAO,CAAC7B,KAAK,CAAC,0BAA0B,EAAE4B,GAAG,CAAC;IAChD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,CAAC;MAC/D,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACA9B,QAAQ,CAAC4B,IAAI,CAACA,IAAI,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAK7B,IAAI,CAAC6B,EAAE,CAAC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,uBAAuB,EAAE4B,GAAG,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMR,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC3B,IAAI,EAAE;IAEX,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAMC,KAAK,CAAC,mDAAmD/B,IAAI,CAAC6B,EAAE,EAAE,CAAC;MAC1F,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBlB,cAAc,CAACgB,IAAI,CAACO,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAE4B,GAAG,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMK,mBAAmB,GAAIC,CAAC,IAAK;IACjC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1B,cAAc,CAAC2B,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAG,MAAOL,CAAC,IAAK;IACrCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBzC,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoC,EAAE;QACjEiB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB,GAAGnC,WAAW;UACdoC,QAAQ,EAAErD,IAAI,CAAC6B;QACjB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBoB,KAAK,CAAC,4BAA4B,CAAC;QACnCxC,cAAc,CAAC,KAAK,CAAC;QACrBI,cAAc,CAAC;UACbC,UAAU,EAAE,EAAE;UACdC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE,EAAE;UACXC,WAAW,EAAE,SAAS;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;QACFC,aAAa,CAAC,CAAC;QACfE,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACL2B,KAAK,CAAC,UAAUtB,IAAI,CAACuB,OAAO,EAAE,CAAC;MACjC;IACF,CAAC,CAAC,OAAOpB,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,wBAAwB,EAAE4B,GAAG,CAAC;MAC5CmB,KAAK,CAAC,uBAAuB,CAAC;IAChC,CAAC,SAAS;MACRhD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkD,UAAU,GAAG,MAAOC,SAAS,IAAK;IACtC,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC0B,SAAS,OAAO,EAAE;QACnFT,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEM,MAAM,EAAE1D,IAAI,CAAC6B;QAAG,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAAC6B,EAAE,EAAE;QACflC,aAAa,CAAC,CAAC;QACfE,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,gCAAgC,EAAE4B,GAAG,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMyB,UAAU,GAAG,MAAAA,CAAOH,SAAS,EAAEI,SAAS,KAAK;IACjD,IAAI;MACF,MAAM/B,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC0B,SAAS,OAAO,EAAE;QACnFT,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBM,MAAM,EAAE1D,IAAI,CAAC6B,EAAE;UACfgC,SAAS,EAAE,CAACA;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI/B,QAAQ,CAAC6B,EAAE,EAAE;QACflC,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,EAAE4B,GAAG,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAM2B,gBAAgB,GAAIvC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,KAAK;QAAE,OAAO,6BAA6B;MAChD,KAAK,QAAQ;QAAE,OAAO,2BAA2B;MACjD,KAAK,MAAM;QAAE,OAAO,+BAA+B;MACnD,KAAK,QAAQ;QAAE,OAAO,yBAAyB;MAC/C;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;;EAED;EACA,MAAMwC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,iBAAiB;QAAE,OAAO,IAAI;MACnC,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;MACzB,OAAO,WAAW;IACpB,CAAC,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxB,OAAO,GAAGA,QAAQ,GAAG,CAAC,WAAW;IACnC,CAAC,MAAM;MACL,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEjF,OAAA;IAAKkF,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBAEjFnF,OAAA;MAAKkF,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DnF,OAAA;QAAKkF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CnF,OAAA;UAAKkF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnF,OAAA;YAAAmF,QAAA,gBACEnF,OAAA;cAAIkF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DvF,OAAA;cAAGkF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACNvF,OAAA;YAAKkF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnF,OAAA;cAAKkF,SAAS,EAAC,8EAA8E;cAAAC,QAAA,eAC3FnF,OAAA;gBAAMkF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAC,eAAG,EAACjE,WAAW,EAAC,SAAO;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNvF,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAMvE,cAAc,CAAC,IAAI,CAAE;cACpCiE,SAAS,EAAC,uKAAuK;cAAAC,QAAA,EAClL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvF,OAAA;MAAKkF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CnF,OAAA;QAAKkF,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnF,OAAA;UAAKkF,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCnF,OAAA;YAAKkF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACnC,CACC;cAAEnD,EAAE,EAAE,OAAO;cAAEyD,KAAK,EAAE,UAAU;cAAE/C,KAAK,EAAExB;YAAY,CAAC,EACtD;cAAEc,EAAE,EAAE,MAAM;cAAEyD,KAAK,EAAE,SAAS;cAAE/C,KAAK,EAAE;YAAE,CAAC,EAC1C;cAAEV,EAAE,EAAE,SAAS;cAAEyD,KAAK,EAAE,WAAW;cAAE/C,KAAK,EAAE;YAAE,CAAC,CAChD,CAACgD,GAAG,CAAEC,GAAG,iBACR3F,OAAA;cAEEwF,OAAO,EAAEA,CAAA,KAAM3E,YAAY,CAAC8E,GAAG,CAAC3D,EAAE,CAAE;cACpCkD,SAAS,EAAE,4CACTtE,SAAS,KAAK+E,GAAG,CAAC3D,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;cAAAmD,QAAA,GAEFQ,GAAG,CAACF,KAAK,EAAC,GAAC,EAACE,GAAG,CAACjD,KAAK,GAAG,CAAC,IAAI,IAAIiD,GAAG,CAACjD,KAAK,GAAG;YAAA,GARzCiD,GAAG,CAAC3D,EAAE;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL7E,KAAK,iBACJV,OAAA;QAAKkF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEnF,OAAA;UAAKkF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnF,OAAA;YAAKkF,SAAS,EAAC,2BAA2B;YAACU,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAX,QAAA,eAC9FnF,OAAA;cAAM+F,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,CAAC,EAAC;YAAmD;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH,CAAC,eACNvF,OAAA;YAAMkF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAEzE;UAAK;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/E,OAAO,iBACNR,OAAA;QAAKkF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnF,OAAA;UAAKkF,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGvF,OAAA;UAAGkF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAGA,CAAC/E,OAAO,iBACPR,OAAA;QAAKkF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDnF,OAAA;UAAKkF,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BnF,OAAA;YAAKkF,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEnF,OAAA;cAAKkF,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CnF,OAAA;gBAAIkF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAEvE;cAAS;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3EvF,OAAA;gBAAGkF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAE/E,QAAQ,CAAC+F,MAAM,EAAC,WAAS;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtC/E,QAAQ,CAAC+F,MAAM,KAAK,CAAC,gBACpBnG,OAAA;gBAAKkF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BnF,OAAA;kBAAKkF,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,eAC/FnF,OAAA;oBAAMkF,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNvF,OAAA;kBAAIkF,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvEvF,OAAA;kBAAGkF,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,sCAAoC,EAACvE,SAAS,EAAC,GAAC;gBAAA;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,GAENnF,QAAQ,CAACsF,GAAG,CAAEhC,OAAO,iBACnB1D,OAAA;gBAEEkF,SAAS,EAAE,yDACT,CAACxB,OAAO,CAAC0C,MAAM,IAAIxF,SAAS,KAAK,OAAO,GAAG,uCAAuC,GAAG,EAAE,EACtF;gBACH4E,OAAO,EAAEA,CAAA,KAAM;kBACbzE,kBAAkB,CAAC2C,OAAO,CAAC;kBAC3B,IAAI,CAACA,OAAO,CAAC0C,MAAM,IAAIxF,SAAS,KAAK,OAAO,EAAE;oBAC5C+C,UAAU,CAACD,OAAO,CAAC1B,EAAE,CAAC;kBACxB;gBACF,CAAE;gBAAAmD,QAAA,eAEFnF,OAAA;kBAAKkF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CnF,OAAA;oBAAKkF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BnF,OAAA;sBAAKkF,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CnF,OAAA;wBAAMkF,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAEjB,kBAAkB,CAACR,OAAO,CAACjC,WAAW;sBAAC;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC1EvF,OAAA;wBAAMkF,SAAS,EAAE,8CAA8CjB,gBAAgB,CAACP,OAAO,CAAChC,QAAQ,CAAC,EAAG;wBAAAyD,QAAA,EACjGzB,OAAO,CAAChC;sBAAQ;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,EACN,CAAC7B,OAAO,CAAC0C,MAAM,IAAIxF,SAAS,KAAK,OAAO,iBACvCZ,OAAA;wBAAMkF,SAAS,EAAC;sBAAkC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAC1D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENvF,OAAA;sBAAKkF,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CnF,OAAA;wBAAMkF,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EACxCvE,SAAS,KAAK,MAAM,GACjB,OAAO8C,OAAO,CAAC2C,iBAAiB,IAAI3C,OAAO,CAAC4C,gBAAgB,EAAE,GAC9D,SAAS5C,OAAO,CAAC6C,eAAe,IAAI7C,OAAO,CAAC8C,cAAc;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE5D,CAAC,eACPvF,OAAA;wBAAMkF,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,GAAC,GAChD,EAACvE,SAAS,KAAK,MAAM,GAAG8C,OAAO,CAAC+C,YAAY,GAAG/C,OAAO,CAACgD,UAAU,EAAC,GACrE;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eAENvF,OAAA;sBAAIkF,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,EAAEzB,OAAO,CAACnC;oBAAO;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChFvF,OAAA;sBAAGkF,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAEzB,OAAO,CAAClC;oBAAO;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eAENvF,OAAA;oBAAKkF,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,gBACjDnF,OAAA;sBAAMkF,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEf,UAAU,CAACV,OAAO,CAACiD,MAAM;oBAAC;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3EvF,OAAA;sBACEwF,OAAO,EAAG5C,CAAC,IAAK;wBACdA,CAAC,CAACgE,eAAe,CAAC,CAAC;wBACnB7C,UAAU,CAACL,OAAO,CAAC1B,EAAE,EAAE0B,OAAO,CAACM,SAAS,CAAC;sBAC3C,CAAE;sBACFkB,SAAS,EAAE,gDACTxB,OAAO,CAACM,SAAS,GAAG,iBAAiB,GAAG,qCAAqC,EAC5E;sBAAAmB,QAAA,EACJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GArDD7B,OAAO,CAAC1B,EAAE;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsDZ,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvF,OAAA;UAAKkF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BrE,eAAe,gBACdd,OAAA;YAAKkF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACvEnF,OAAA;cAAKkF,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnF,OAAA;gBAAIkF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEvF,OAAA;gBACEwF,OAAO,EAAEA,CAAA,KAAMzE,kBAAkB,CAAC,IAAI,CAAE;gBACxCmE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC9C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnF,OAAA;gBAAAmF,QAAA,gBACEnF,OAAA;kBAAOkF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpEvF,OAAA;kBAAGkF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAErE,eAAe,CAACS;gBAAO;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eAENvF,OAAA;gBAAAmF,QAAA,gBACEnF,OAAA;kBAAOkF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EACjDvE,SAAS,KAAK,MAAM,GAAG,IAAI,GAAG;gBAAM;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACRvF,OAAA;kBAAGkF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EACzBvE,SAAS,KAAK,MAAM,GACjB,GAAGE,eAAe,CAACuF,iBAAiB,IAAIvF,eAAe,CAACwF,gBAAgB,EAAE,GAC1E,GAAGxF,eAAe,CAACyF,eAAe,IAAIzF,eAAe,CAAC0F,cAAc;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzE,CAAC,eACJvF,OAAA;kBAAGkF,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAC5CvE,SAAS,KAAK,MAAM,GAAGE,eAAe,CAAC2F,YAAY,GAAG3F,eAAe,CAAC4F;gBAAU;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENvF,OAAA;gBAAAmF,QAAA,gBACEnF,OAAA;kBAAOkF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpEvF,OAAA;kBAAKkF,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,eAC7CnF,OAAA;oBAAGkF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAErE,eAAe,CAACU;kBAAO;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvF,OAAA;gBAAKkF,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAC5DnF,OAAA;kBAAAmF,QAAA,GAAM,eAAG,EAACf,UAAU,CAACtD,eAAe,CAAC6F,MAAM,CAAC;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDvF,OAAA;kBAAMkF,SAAS,EAAE,kCAAkCjB,gBAAgB,CAACnD,eAAe,CAACY,QAAQ,CAAC,EAAG;kBAAAyD,QAAA,EAC7FrE,eAAe,CAACY;gBAAQ;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENvF,OAAA;YAAKkF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,eACvEnF,OAAA;cAAKkF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnF,OAAA;gBAAKkF,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,eAC/FnF,OAAA;kBAAMkF,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNvF,OAAA;gBAAIkF,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,GAAC,WAAS,EAAChF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,IAAI;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/EvF,OAAA;gBAAGkF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,QAAM,EAAChF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,IAAI;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEvF,OAAA;gBAAGkF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvE,WAAW,iBACVhB,OAAA;MAAKkF,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FnF,OAAA;QAAKkF,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAC5FnF,OAAA;UAAKkF,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3CnF,OAAA;YAAKkF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnF,OAAA;cAAIkF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEvF,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAMvE,cAAc,CAAC,KAAK,CAAE;cACrCiE,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvF,OAAA;UAAM8G,QAAQ,EAAE7D,iBAAkB;UAACiC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1DnF,OAAA;YAAKkF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnF,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAOkF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvF,OAAA;gBACE6C,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAE1B,WAAW,CAACE,UAAW;gBAC9ByF,QAAQ,EAAEpE,mBAAoB;gBAC9BqE,QAAQ;gBACR9B,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,gBAExHnF,OAAA;kBAAQ8C,KAAK,EAAC,EAAE;kBAAAqC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC5CjF,KAAK,CAACoF,GAAG,CAAEjD,CAAC,iBACXzC,OAAA;kBAAmB8C,KAAK,EAAEL,CAAC,CAACT,EAAG;kBAAAmD,QAAA,GAC5B1C,CAAC,CAACwE,SAAS,EAAC,GAAC,EAACxE,CAAC,CAACyE,QAAQ,EAAC,IAAE,EAACzE,CAAC,CAACoE,IAAI,EAAC,GACtC;gBAAA,GAFapE,CAAC,CAACT,EAAE;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvF,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAOkF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvF,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE1B,WAAW,CAACM,QAAS;gBAC5BqF,QAAQ,EAAEpE,mBAAoB;gBAC9BuC,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,gBAExHnF,OAAA;kBAAQ8C,KAAK,EAAC,KAAK;kBAAAqC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCvF,OAAA;kBAAQ8C,KAAK,EAAC,QAAQ;kBAAAqC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCvF,OAAA;kBAAQ8C,KAAK,EAAC,MAAM;kBAAAqC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCvF,OAAA;kBAAQ8C,KAAK,EAAC,QAAQ;kBAAAqC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvF,OAAA;YAAKkF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnF,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAOkF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvF,OAAA;gBACE6C,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAE1B,WAAW,CAACK,WAAY;gBAC/BsF,QAAQ,EAAEpE,mBAAoB;gBAC9BuC,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,gBAExHnF,OAAA;kBAAQ8C,KAAK,EAAC,SAAS;kBAAAqC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CvF,OAAA;kBAAQ8C,KAAK,EAAC,QAAQ;kBAAAqC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCvF,OAAA;kBAAQ8C,KAAK,EAAC,iBAAiB;kBAAAqC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3DvF,OAAA;kBAAQ8C,KAAK,EAAC,QAAQ;kBAAAqC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvF,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAOkF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvF,OAAA;gBACEmE,IAAI,EAAC,MAAM;gBACXtB,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE1B,WAAW,CAACO,SAAU;gBAC7BoF,QAAQ,EAAEpE,mBAAoB;gBAC9BwE,WAAW,EAAC,gCAAgC;gBAC5CjC,SAAS,EAAC;cAA8G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvF,OAAA;YAAAmF,QAAA,gBACEnF,OAAA;cAAOkF,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvF,OAAA;cACEmE,IAAI,EAAC,MAAM;cACXtB,IAAI,EAAC,SAAS;cACdC,KAAK,EAAE1B,WAAW,CAACG,OAAQ;cAC3BwF,QAAQ,EAAEpE,mBAAoB;cAC9BqE,QAAQ;cACRG,WAAW,EAAC,uBAAuB;cACnCjC,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvF,OAAA;YAAAmF,QAAA,gBACEnF,OAAA;cAAOkF,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvF,OAAA;cACE6C,IAAI,EAAC,SAAS;cACdC,KAAK,EAAE1B,WAAW,CAACI,OAAQ;cAC3BuF,QAAQ,EAAEpE,mBAAoB;cAC9BqE,QAAQ;cACRI,IAAI,EAAE,CAAE;cACRD,WAAW,EAAC,2BAA2B;cACvCjC,SAAS,EAAC;YAA0H;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvF,OAAA;YAAKkF,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAChFnF,OAAA;cACEmE,IAAI,EAAC,QAAQ;cACbqB,OAAO,EAAEA,CAAA,KAAMvE,cAAc,CAAC,KAAK,CAAE;cACrCiE,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EACzG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvF,OAAA;cACEmE,IAAI,EAAC,QAAQ;cACbkD,QAAQ,EAAE7G,OAAQ;cAClB0E,SAAS,EAAC,mLAAmL;cAAAC,QAAA,EAE5L3E,OAAO,GAAG,YAAY,GAAG;YAAiB;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACrF,EAAA,CAxlBQD,QAAQ;EAAA,QACEH,OAAO;AAAA;AAAAwH,EAAA,GADjBrH,QAAQ;AA0lBjB,eAAeA,QAAQ;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}