{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\Patients.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Patients() {\n  _s();\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingPatient, setEditingPatient] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filteredPatients, setFilteredPatients] = useState([]);\n  const [showPrescriptionModal, setShowPrescriptionModal] = useState(false);\n  const [selectedPatientForPrescription, setSelectedPatientForPrescription] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    nationalId: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    gender: '',\n    address: '',\n    emergencyContact: '',\n    bloodType: '',\n    allergies: '',\n    medicalHistory: ''\n  });\n\n  // Prescription form state\n  const [prescriptionData, setPrescriptionData] = useState({\n    patientId: '',\n    doctorName: '',\n    diagnosis: '',\n    medications: [{\n      name: '',\n      dosage: '',\n      frequency: '',\n      duration: '',\n      instructions: ''\n    }],\n    notes: '',\n    prescriptionDate: new Date().toISOString().split('T')[0]\n  });\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patients from API\n  const fetchPatients = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n        setFilteredPatients(data.data);\n      }\n    } catch (err) {\n      setError('Failed to fetch patients. Make sure the backend server is running.');\n      console.error('Error fetching patients:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load patients on component mount\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  // Filter patients based on search query\n  useEffect(() => {\n    if (searchQuery.trim() === '') {\n      setFilteredPatients(patients);\n    } else {\n      const filtered = patients.filter(patient => patient.nationalId.includes(searchQuery) || patient.firstName.toLowerCase().includes(searchQuery.toLowerCase()) || patient.lastName.toLowerCase().includes(searchQuery.toLowerCase()) || patient.email.toLowerCase().includes(searchQuery.toLowerCase()) || patient.phone.includes(searchQuery));\n      setFilteredPatients(filtered);\n    }\n  }, [searchQuery, patients]);\n\n  // Handle form input changes\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      nationalId: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      dateOfBirth: '',\n      gender: '',\n      address: '',\n      emergencyContact: '',\n      bloodType: '',\n      allergies: '',\n      medicalHistory: ''\n    });\n    setEditingPatient(null);\n    setShowAddForm(false);\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      if (editingPatient) {\n        // Update existing patient\n        const response = await fetch(`${API_BASE_URL}/patients/${editingPatient.nationalId}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(formData)\n        });\n        const data = await response.json();\n        if (data.success) {\n          await fetchPatients();\n          resetForm();\n          setError('');\n        } else {\n          setError(data.message || 'Failed to update patient');\n        }\n      } else {\n        // Create new patient\n        const response = await fetch(`${API_BASE_URL}/patients`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(formData)\n        });\n        const data = await response.json();\n        if (data.success) {\n          await fetchPatients();\n          resetForm();\n          setError('');\n        } else {\n          setError(data.message || 'Failed to create patient');\n        }\n      }\n    } catch (err) {\n      setError('Failed to save patient. Please check your connection.');\n      console.error('Error saving patient:', err);\n    }\n  };\n\n  // Handle edit patient\n  const handleEdit = patient => {\n    setFormData({\n      nationalId: patient.nationalId,\n      firstName: patient.firstName,\n      lastName: patient.lastName,\n      email: patient.email,\n      phone: patient.phone,\n      dateOfBirth: patient.dateOfBirth,\n      gender: patient.gender,\n      address: patient.address,\n      emergencyContact: patient.emergencyContact,\n      bloodType: patient.bloodType,\n      allergies: patient.allergies,\n      medicalHistory: patient.medicalHistory\n    });\n    setEditingPatient(patient);\n    setShowAddForm(true);\n  };\n\n  // Handle delete patient\n  const handleDelete = async nationalId => {\n    if (window.confirm('Are you sure you want to delete this patient?')) {\n      try {\n        const response = await fetch(`${API_BASE_URL}/patients/${nationalId}`, {\n          method: 'DELETE'\n        });\n        const data = await response.json();\n        if (data.success) {\n          await fetchPatients();\n          setError('');\n        } else {\n          setError(data.message || 'Failed to delete patient');\n        }\n      } catch (err) {\n        setError('Failed to delete patient. Please check your connection.');\n        console.error('Error deleting patient:', err);\n      }\n    }\n  };\n\n  // Handle prescription modal\n  const handlePrescription = patient => {\n    setSelectedPatientForPrescription(patient);\n    setPrescriptionData({\n      ...prescriptionData,\n      patientId: patient.nationalId,\n      prescriptionDate: new Date().toISOString().split('T')[0]\n    });\n    setShowPrescriptionModal(true);\n  };\n\n  // Handle prescription form input changes\n  const handlePrescriptionInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setPrescriptionData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle medication changes\n  const handleMedicationChange = (index, field, value) => {\n    const updatedMedications = [...prescriptionData.medications];\n    updatedMedications[index][field] = value;\n    setPrescriptionData(prev => ({\n      ...prev,\n      medications: updatedMedications\n    }));\n  };\n\n  // Add new medication\n  const addMedication = () => {\n    setPrescriptionData(prev => ({\n      ...prev,\n      medications: [...prev.medications, {\n        name: '',\n        dosage: '',\n        frequency: '',\n        duration: '',\n        instructions: ''\n      }]\n    }));\n  };\n\n  // Remove medication\n  const removeMedication = index => {\n    if (prescriptionData.medications.length > 1) {\n      const updatedMedications = prescriptionData.medications.filter((_, i) => i !== index);\n      setPrescriptionData(prev => ({\n        ...prev,\n        medications: updatedMedications\n      }));\n    }\n  };\n\n  // Reset prescription form\n  const resetPrescriptionForm = () => {\n    setPrescriptionData({\n      patientId: '',\n      doctorName: '',\n      diagnosis: '',\n      medications: [{\n        name: '',\n        dosage: '',\n        frequency: '',\n        duration: '',\n        instructions: ''\n      }],\n      notes: '',\n      prescriptionDate: new Date().toISOString().split('T')[0]\n    });\n    setSelectedPatientForPrescription(null);\n    setShowPrescriptionModal(false);\n  };\n\n  // Handle prescription submission\n  const handlePrescriptionSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Prepare prescription data for API\n      const prescriptionPayload = {\n        nationalId: selectedPatientForPrescription.nationalId,\n        doctorId: 'DOC001',\n        // You might want to get this from user context or form\n        prescriptionDate: prescriptionData.prescriptionDate,\n        diagnosis: prescriptionData.diagnosis,\n        medications: prescriptionData.medications.filter(med => med.name.trim() !== ''),\n        notes: prescriptionData.notes,\n        validUntil: null,\n        // You can add this field to the form if needed\n        createdBy: prescriptionData.doctorName\n      };\n      const response = await fetch('http://localhost:5000/api/prescriptions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(prescriptionPayload)\n      });\n      const result = await response.json();\n      if (result.success) {\n        // Show success message with SMS status\n        let successMessage = 'Prescription created successfully!';\n        if (result.data.smsNotification) {\n          if (result.data.smsNotification.sent) {\n            successMessage += `\\n\\n📱 SMS notification sent to ${result.data.smsNotification.to}`;\n          } else {\n            successMessage += `\\n\\n⚠️ SMS notification failed: ${result.data.smsNotification.message}`;\n          }\n        }\n        alert(successMessage);\n        console.log('Prescription created:', result.data);\n        resetPrescriptionForm();\n\n        // Refresh patients list if needed\n        fetchPatients();\n      } else {\n        throw new Error(result.message || 'Failed to create prescription');\n      }\n    } catch (err) {\n      setError('Failed to create prescription. Please try again.');\n      console.error('Error creating prescription:', err);\n      alert('Error creating prescription: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 py-20\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-full text-sm font-semibold mb-4\",\n          children: \"\\uD83D\\uDC64 Patient Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n          children: \"Patient Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Comprehensive patient management system for healthcare providers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-600 mr-2\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-6 mb-8 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 max-w-md\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search patients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"absolute left-3 top-2.5 text-gray-400\",\n                children: \"\\uD83D\\uDD0D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddForm(true),\n            className: \"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\",\n            children: \"\\u2795 Add New Patient\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-purple-700\",\n              children: patients.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-purple-600 text-sm\",\n              children: \"Total Patients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-blue-700\",\n              children: filteredPatients.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-blue-600 text-sm\",\n              children: \"Search Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-green-700\",\n              children: patients.filter(p => new Date(p.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-600 text-sm\",\n              children: \"New This Month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 text-center border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading patients...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Patient List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this), filteredPatients.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-6xl mb-4 block\",\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-700 mb-2\",\n            children: \"No Patients Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: searchQuery ? 'No patients match your search criteria.' : 'Start by adding your first patient.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"National ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Contact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Prescription\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: filteredPatients.map(patient => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white font-semibold\",\n                        children: [patient.firstName.charAt(0), patient.lastName.charAt(0)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 450,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [patient.firstName, \" \", patient.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 455,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Patient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: patient.nationalId\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"National ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-900\",\n                    children: patient.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: patient.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-900\",\n                    children: patient.gender && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mr-2\",\n                      children: [\"\\uD83D\\uDC64 \", patient.gender]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"\\uD83C\\uDF82 \", new Date(patient.dateOfBirth).toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handlePrescription(patient),\n                    className: \"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg text-sm font-medium\",\n                    children: \"\\uD83D\\uDC8A Create Prescription\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEdit(patient),\n                      className: \"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg transition-colors\",\n                      children: \"\\u270F\\uFE0F Edit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDelete(patient.nationalId),\n                      className: \"bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-lg transition-colors\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 25\n                }, this)]\n              }, patient.nationalId, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 11\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: editingPatient ? '✏️ Edit Patient' : '➕ Add New Patient'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetForm,\n                className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"\\uD83D\\uDC64 Personal Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"National ID *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"nationalId\",\n                    value: formData.nationalId,\n                    onChange: handleInputChange,\n                    required: true,\n                    disabled: editingPatient ? true : false,\n                    placeholder: \"Enter 10-20 digit national ID\",\n                    pattern: \"[0-9]{10,20}\",\n                    title: \"National ID must be 10-20 digits\",\n                    className: `w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${editingPatient ? 'bg-gray-100 cursor-not-allowed' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this), editingPatient && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"National ID cannot be changed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"First Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"firstName\",\n                    value: formData.firstName,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Last Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"lastName\",\n                    value: formData.lastName,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phone\",\n                    value: formData.phone,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Date of Birth *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"dateOfBirth\",\n                    value: formData.dateOfBirth,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Gender\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"gender\",\n                    value: formData.gender,\n                    onChange: handleInputChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Gender\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Male\",\n                      children: \"Male\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Female\",\n                      children: \"Female\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Other\",\n                      children: \"Other\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"address\",\n                    value: formData.address,\n                    onChange: handleInputChange,\n                    rows: \"3\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"\\uD83C\\uDFE5 Medical Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Emergency Contact\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"emergencyContact\",\n                    value: formData.emergencyContact,\n                    onChange: handleInputChange,\n                    placeholder: \"Name - Phone Number\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Blood Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"bloodType\",\n                    value: formData.bloodType,\n                    onChange: handleInputChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Blood Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"A+\",\n                      children: \"A+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"A-\",\n                      children: \"A-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"B+\",\n                      children: \"B+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"B-\",\n                      children: \"B-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"AB+\",\n                      children: \"AB+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"AB-\",\n                      children: \"AB-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"O+\",\n                      children: \"O+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 672,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"O-\",\n                      children: \"O-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Allergies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"allergies\",\n                    value: formData.allergies,\n                    onChange: handleInputChange,\n                    rows: \"3\",\n                    placeholder: \"List any known allergies or 'None'\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Medical History\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"medicalHistory\",\n                    value: formData.medicalHistory,\n                    onChange: handleInputChange,\n                    rows: \"4\",\n                    placeholder: \"Previous conditions, surgeries, medications, etc.\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: resetForm,\n                className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all duration-300 shadow-md hover:shadow-lg\",\n                children: editingPatient ? 'Update Patient' : 'Add Patient'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this), showPrescriptionModal && selectedPatientForPrescription && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 flex items-center justify-between flex-shrink-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white\",\n              children: \"\\uD83D\\uDC8A Create Prescription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetPrescriptionForm,\n              className: \"text-white hover:text-gray-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handlePrescriptionSubmit,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-2\",\n                  children: \"Patient Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: \"Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900\",\n                      children: [selectedPatientForPrescription.firstName, \" \", selectedPatientForPrescription.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: \"National ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 751,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900\",\n                      children: selectedPatientForPrescription.nationalId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 752,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: \"Date of Birth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 755,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900\",\n                      children: new Date(selectedPatientForPrescription.dateOfBirth).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: \"Blood Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 759,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900\",\n                      children: selectedPatientForPrescription.bloodType || 'Not specified'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 760,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 21\n                }, this), selectedPatientForPrescription.allergies && selectedPatientForPrescription.allergies !== 'None' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-red-700\",\n                    children: \"\\u26A0\\uFE0F Allergies\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: selectedPatientForPrescription.allergies\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Doctor Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"doctorName\",\n                    value: prescriptionData.doctorName,\n                    onChange: handlePrescriptionInputChange,\n                    required: true,\n                    placeholder: \"Enter prescribing doctor's name\",\n                    className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Prescription Date *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"date\",\n                    name: \"prescriptionDate\",\n                    value: prescriptionData.prescriptionDate,\n                    onChange: handlePrescriptionInputChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Diagnosis *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"diagnosis\",\n                  value: prescriptionData.diagnosis,\n                  onChange: handlePrescriptionInputChange,\n                  required: true,\n                  rows: \"3\",\n                  placeholder: \"Enter patient's diagnosis...\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 resize-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Medications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: addMedication,\n                    className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors\",\n                    children: \"\\u2795 Add Medication\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 21\n                }, this), prescriptionData.medications.map((medication, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-4 rounded-lg mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900\",\n                      children: [\"Medication \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 27\n                    }, this), prescriptionData.medications.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => removeMedication(index),\n                      className: \"text-red-500 hover:text-red-700 text-sm\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F Remove\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Medication Name *\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 842,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: medication.name,\n                        onChange: e => handleMedicationChange(index, 'name', e.target.value),\n                        required: true,\n                        placeholder: \"e.g., Paracetamol\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 843,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 841,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Dosage *\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 854,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: medication.dosage,\n                        onChange: e => handleMedicationChange(index, 'dosage', e.target.value),\n                        required: true,\n                        placeholder: \"e.g., 500mg\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 853,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Frequency *\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 866,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: medication.frequency,\n                        onChange: e => handleMedicationChange(index, 'frequency', e.target.value),\n                        required: true,\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select frequency\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 873,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Once daily\",\n                          children: \"Once daily\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 874,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Twice daily\",\n                          children: \"Twice daily\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 875,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Three times daily\",\n                          children: \"Three times daily\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 876,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Four times daily\",\n                          children: \"Four times daily\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Every 4 hours\",\n                          children: \"Every 4 hours\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 878,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Every 6 hours\",\n                          children: \"Every 6 hours\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Every 8 hours\",\n                          children: \"Every 8 hours\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 880,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"As needed\",\n                          children: \"As needed\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 881,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 867,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Duration *\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 886,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: medication.duration,\n                        onChange: e => handleMedicationChange(index, 'duration', e.target.value),\n                        required: true,\n                        placeholder: \"e.g., 7 days\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 885,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1\",\n                      children: \"Special Instructions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: medication.instructions,\n                      onChange: e => handleMedicationChange(index, 'instructions', e.target.value),\n                      rows: \"2\",\n                      placeholder: \"e.g., Take with food, avoid alcohol...\",\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 resize-none\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 900,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Additional Notes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"notes\",\n                  value: prescriptionData.notes,\n                  onChange: handlePrescriptionInputChange,\n                  rows: \"3\",\n                  placeholder: \"Any additional instructions or notes...\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 resize-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-4 pt-4 border-t border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: resetPrescriptionForm,\n                  className: \"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-all\",\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:scale-105\",\n                  children: \"Create Prescription\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n}\n_s(Patients, \"sOY+Lg/t8cWIGZxnHhaxwfU4IXg=\");\n_c = Patients;\nexport default Patients;\nvar _c;\n$RefreshReg$(_c, \"Patients\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "Patients", "_s", "patients", "setPatients", "loading", "setLoading", "error", "setError", "showAddForm", "setShowAddForm", "editingPatient", "setEditingPatient", "searchQuery", "setSearch<PERSON>uery", "filteredPatients", "setFilteredPatients", "showPrescriptionModal", "setShowPrescriptionModal", "selectedPatientForPrescription", "setSelectedPatientForPrescription", "formData", "setFormData", "nationalId", "firstName", "lastName", "email", "phone", "dateOfBirth", "gender", "address", "emergencyContact", "bloodType", "allergies", "medicalHistory", "prescriptionData", "setPrescriptionData", "patientId", "<PERSON><PERSON><PERSON>", "diagnosis", "medications", "name", "dosage", "frequency", "duration", "instructions", "notes", "prescriptionDate", "Date", "toISOString", "split", "API_BASE_URL", "fetchPatients", "response", "fetch", "data", "json", "success", "err", "console", "trim", "filtered", "filter", "patient", "includes", "toLowerCase", "handleInputChange", "e", "value", "target", "prev", "resetForm", "handleSubmit", "preventDefault", "method", "headers", "body", "JSON", "stringify", "message", "handleEdit", "handleDelete", "window", "confirm", "handlePrescription", "handlePrescriptionInputChange", "handleMedicationChange", "index", "field", "updatedMedications", "addMedication", "removeMedication", "length", "_", "i", "resetPrescriptionForm", "handlePrescriptionSubmit", "prescriptionPayload", "doctorId", "med", "validUntil", "created<PERSON>y", "result", "successMessage", "smsNotification", "sent", "to", "alert", "log", "Error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "p", "createdAt", "now", "map", "char<PERSON>t", "toLocaleDateString", "onSubmit", "required", "disabled", "pattern", "title", "rows", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "medication", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Healthcare/frontend/src/pages/Patients.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\nfunction Patients() {\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingPatient, setEditingPatient] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filteredPatients, setFilteredPatients] = useState([]);\n  const [showPrescriptionModal, setShowPrescriptionModal] = useState(false);\n  const [selectedPatientForPrescription, setSelectedPatientForPrescription] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    nationalId: '',\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    gender: '',\n    address: '',\n    emergencyContact: '',\n    bloodType: '',\n    allergies: '',\n    medicalHistory: ''\n  });\n\n  // Prescription form state\n  const [prescriptionData, setPrescriptionData] = useState({\n    patientId: '',\n    doctorName: '',\n    diagnosis: '',\n    medications: [\n      {\n        name: '',\n        dosage: '',\n        frequency: '',\n        duration: '',\n        instructions: ''\n      }\n    ],\n    notes: '',\n    prescriptionDate: new Date().toISOString().split('T')[0]\n  });\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patients from API\n  const fetchPatients = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n        setFilteredPatients(data.data);\n      }\n    } catch (err) {\n      setError('Failed to fetch patients. Make sure the backend server is running.');\n      console.error('Error fetching patients:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load patients on component mount\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  // Filter patients based on search query\n  useEffect(() => {\n    if (searchQuery.trim() === '') {\n      setFilteredPatients(patients);\n    } else {\n      const filtered = patients.filter(patient =>\n        patient.nationalId.includes(searchQuery) ||\n        patient.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        patient.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        patient.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        patient.phone.includes(searchQuery)\n      );\n      setFilteredPatients(filtered);\n    }\n  }, [searchQuery, patients]);\n\n  // Handle form input changes\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      nationalId: '',\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      dateOfBirth: '',\n      gender: '',\n      address: '',\n      emergencyContact: '',\n      bloodType: '',\n      allergies: '',\n      medicalHistory: ''\n    });\n    setEditingPatient(null);\n    setShowAddForm(false);\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      if (editingPatient) {\n        // Update existing patient\n        const response = await fetch(`${API_BASE_URL}/patients/${editingPatient.nationalId}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(formData),\n        });\n        const data = await response.json();\n        if (data.success) {\n          await fetchPatients();\n          resetForm();\n          setError('');\n        } else {\n          setError(data.message || 'Failed to update patient');\n        }\n      } else {\n        // Create new patient\n        const response = await fetch(`${API_BASE_URL}/patients`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(formData),\n        });\n        const data = await response.json();\n        if (data.success) {\n          await fetchPatients();\n          resetForm();\n          setError('');\n        } else {\n          setError(data.message || 'Failed to create patient');\n        }\n      }\n    } catch (err) {\n      setError('Failed to save patient. Please check your connection.');\n      console.error('Error saving patient:', err);\n    }\n  };\n\n  // Handle edit patient\n  const handleEdit = (patient) => {\n    setFormData({\n      nationalId: patient.nationalId,\n      firstName: patient.firstName,\n      lastName: patient.lastName,\n      email: patient.email,\n      phone: patient.phone,\n      dateOfBirth: patient.dateOfBirth,\n      gender: patient.gender,\n      address: patient.address,\n      emergencyContact: patient.emergencyContact,\n      bloodType: patient.bloodType,\n      allergies: patient.allergies,\n      medicalHistory: patient.medicalHistory\n    });\n    setEditingPatient(patient);\n    setShowAddForm(true);\n  };\n\n  // Handle delete patient\n  const handleDelete = async (nationalId) => {\n    if (window.confirm('Are you sure you want to delete this patient?')) {\n      try {\n        const response = await fetch(`${API_BASE_URL}/patients/${nationalId}`, {\n          method: 'DELETE',\n        });\n        const data = await response.json();\n        if (data.success) {\n          await fetchPatients();\n          setError('');\n        } else {\n          setError(data.message || 'Failed to delete patient');\n        }\n      } catch (err) {\n        setError('Failed to delete patient. Please check your connection.');\n        console.error('Error deleting patient:', err);\n      }\n    }\n  };\n\n  // Handle prescription modal\n  const handlePrescription = (patient) => {\n    setSelectedPatientForPrescription(patient);\n    setPrescriptionData({\n      ...prescriptionData,\n      patientId: patient.nationalId,\n      prescriptionDate: new Date().toISOString().split('T')[0]\n    });\n    setShowPrescriptionModal(true);\n  };\n\n  // Handle prescription form input changes\n  const handlePrescriptionInputChange = (e) => {\n    const { name, value } = e.target;\n    setPrescriptionData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle medication changes\n  const handleMedicationChange = (index, field, value) => {\n    const updatedMedications = [...prescriptionData.medications];\n    updatedMedications[index][field] = value;\n    setPrescriptionData(prev => ({\n      ...prev,\n      medications: updatedMedications\n    }));\n  };\n\n  // Add new medication\n  const addMedication = () => {\n    setPrescriptionData(prev => ({\n      ...prev,\n      medications: [\n        ...prev.medications,\n        {\n          name: '',\n          dosage: '',\n          frequency: '',\n          duration: '',\n          instructions: ''\n        }\n      ]\n    }));\n  };\n\n  // Remove medication\n  const removeMedication = (index) => {\n    if (prescriptionData.medications.length > 1) {\n      const updatedMedications = prescriptionData.medications.filter((_, i) => i !== index);\n      setPrescriptionData(prev => ({\n        ...prev,\n        medications: updatedMedications\n      }));\n    }\n  };\n\n  // Reset prescription form\n  const resetPrescriptionForm = () => {\n    setPrescriptionData({\n      patientId: '',\n      doctorName: '',\n      diagnosis: '',\n      medications: [\n        {\n          name: '',\n          dosage: '',\n          frequency: '',\n          duration: '',\n          instructions: ''\n        }\n      ],\n      notes: '',\n      prescriptionDate: new Date().toISOString().split('T')[0]\n    });\n    setSelectedPatientForPrescription(null);\n    setShowPrescriptionModal(false);\n  };\n\n  // Handle prescription submission\n  const handlePrescriptionSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Prepare prescription data for API\n      const prescriptionPayload = {\n        nationalId: selectedPatientForPrescription.nationalId,\n        doctorId: 'DOC001', // You might want to get this from user context or form\n        prescriptionDate: prescriptionData.prescriptionDate,\n        diagnosis: prescriptionData.diagnosis,\n        medications: prescriptionData.medications.filter(med => med.name.trim() !== ''),\n        notes: prescriptionData.notes,\n        validUntil: null, // You can add this field to the form if needed\n        createdBy: prescriptionData.doctorName\n      };\n\n      const response = await fetch('http://localhost:5000/api/prescriptions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(prescriptionPayload),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Show success message with SMS status\n        let successMessage = 'Prescription created successfully!';\n\n        if (result.data.smsNotification) {\n          if (result.data.smsNotification.sent) {\n            successMessage += `\\n\\n📱 SMS notification sent to ${result.data.smsNotification.to}`;\n          } else {\n            successMessage += `\\n\\n⚠️ SMS notification failed: ${result.data.smsNotification.message}`;\n          }\n        }\n\n        alert(successMessage);\n        console.log('Prescription created:', result.data);\n        resetPrescriptionForm();\n\n        // Refresh patients list if needed\n        fetchPatients();\n      } else {\n        throw new Error(result.message || 'Failed to create prescription');\n      }\n    } catch (err) {\n      setError('Failed to create prescription. Please try again.');\n      console.error('Error creating prescription:', err);\n      alert('Error creating prescription: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return(\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 py-20\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Header Section */}\n        <div className=\"text-center mb-12\">\n          <span className=\"inline-block px-4 py-2 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-full text-sm font-semibold mb-4\">\n            👤 Patient Management System\n          </span>\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">Patient Management</h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Comprehensive patient management system for healthcare providers\n          </p>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n            <div className=\"flex items-center\">\n              <span className=\"text-red-600 mr-2\">⚠️</span>\n              <p className=\"text-red-700\">{error}</p>\n            </div>\n          </div>\n        )}\n\n        {/* Controls Section */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-6 mb-8 border border-gray-100\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            {/* Search Bar */}\n            <div className=\"flex-1 max-w-md\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search patients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                />\n                <span className=\"absolute left-3 top-2.5 text-gray-400\">🔍</span>\n              </div>\n            </div>\n\n            {/* Add Patient Button */}\n            <button\n              onClick={() => setShowAddForm(true)}\n              className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\"\n            >\n              ➕ Add New Patient\n            </button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200\">\n              <div className=\"text-2xl font-bold text-purple-700\">{patients.length}</div>\n              <div className=\"text-purple-600 text-sm\">Total Patients</div>\n            </div>\n            <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200\">\n              <div className=\"text-2xl font-bold text-blue-700\">{filteredPatients.length}</div>\n              <div className=\"text-blue-600 text-sm\">Search Results</div>\n            </div>\n            <div className=\"bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200\">\n              <div className=\"text-2xl font-bold text-green-700\">\n                {patients.filter(p => new Date(p.createdAt) > new Date(Date.now() - 30*24*60*60*1000)).length}\n              </div>\n              <div className=\"text-green-600 text-sm\">New This Month</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Patient List */}\n        {loading ? (\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 text-center border border-gray-100\">\n            <div className=\"animate-spin w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading patients...</p>\n          </div>\n        ) : (\n          <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Patient List</h2>\n            </div>\n\n            {filteredPatients.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <span className=\"text-6xl mb-4 block\">👥</span>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">No Patients Found</h3>\n                <p className=\"text-gray-500\">\n                  {searchQuery ? 'No patients match your search criteria.' : 'Start by adding your first patient.'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Patient</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">National ID</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Contact</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Details</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Prescription</th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {filteredPatients.map((patient) => (\n                      <tr key={patient.nationalId} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-3\">\n                              <span className=\"text-white font-semibold\">\n                                {patient.firstName.charAt(0)}{patient.lastName.charAt(0)}\n                              </span>\n                            </div>\n                            <div>\n                              <div className=\"text-sm font-medium text-gray-900\">\n                                {patient.firstName} {patient.lastName}\n                              </div>\n                              <div className=\"text-sm text-gray-500\">Patient</div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm font-medium text-gray-900\">{patient.nationalId}</div>\n                          <div className=\"text-sm text-gray-500\">National ID</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900\">{patient.email}</div>\n                          <div className=\"text-sm text-gray-500\">{patient.phone}</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900\">\n                            {patient.gender && <span className=\"mr-2\">👤 {patient.gender}</span>}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            🎂 {new Date(patient.dateOfBirth).toLocaleDateString()}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <button\n                            onClick={() => handlePrescription(patient)}\n                            className=\"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg text-sm font-medium\"\n                          >\n                            💊 Create Prescription\n                          </button>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleEdit(patient)}\n                              className=\"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg transition-colors\"\n                            >\n                              ✏️ Edit\n                            </button>\n                            <button\n                              onClick={() => handleDelete(patient.nationalId)}\n                              className=\"bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-lg transition-colors\"\n                            >\n                              🗑️ Delete\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Add/Edit Patient Modal */}\n        {showAddForm && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <h2 className=\"text-2xl font-bold text-gray-900\">\n                    {editingPatient ? '✏️ Edit Patient' : '➕ Add New Patient'}\n                  </h2>\n                  <button\n                    onClick={resetForm}\n                    className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                  >\n                    ✕\n                  </button>\n                </div>\n              </div>\n\n              <form onSubmit={handleSubmit} className=\"p-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Personal Information */}\n                  <div className=\"space-y-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">👤 Personal Information</h3>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">National ID *</label>\n                      <input\n                        type=\"text\"\n                        name=\"nationalId\"\n                        value={formData.nationalId}\n                        onChange={handleInputChange}\n                        required\n                        disabled={editingPatient ? true : false}\n                        placeholder=\"Enter 10-20 digit national ID\"\n                        pattern=\"[0-9]{10,20}\"\n                        title=\"National ID must be 10-20 digits\"\n                        className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${editingPatient ? 'bg-gray-100 cursor-not-allowed' : ''}`}\n                      />\n                      {editingPatient && (\n                        <p className=\"text-xs text-gray-500 mt-1\">National ID cannot be changed</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">First Name *</label>\n                      <input\n                        type=\"text\"\n                        name=\"firstName\"\n                        value={formData.firstName}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Last Name *</label>\n                      <input\n                        type=\"text\"\n                        name=\"lastName\"\n                        value={formData.lastName}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email *</label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone *</label>\n                      <input\n                        type=\"tel\"\n                        name=\"phone\"\n                        value={formData.phone}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Date of Birth *</label>\n                      <input\n                        type=\"date\"\n                        name=\"dateOfBirth\"\n                        value={formData.dateOfBirth}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Gender</label>\n                      <select\n                        name=\"gender\"\n                        value={formData.gender}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      >\n                        <option value=\"\">Select Gender</option>\n                        <option value=\"Male\">Male</option>\n                        <option value=\"Female\">Female</option>\n                        <option value=\"Other\">Other</option>\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Address</label>\n                      <textarea\n                        name=\"address\"\n                        value={formData.address}\n                        onChange={handleInputChange}\n                        rows=\"3\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n                  </div>\n\n                  {/* Medical Information */}\n                  <div className=\"space-y-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">🏥 Medical Information</h3>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Emergency Contact</label>\n                      <input\n                        type=\"text\"\n                        name=\"emergencyContact\"\n                        value={formData.emergencyContact}\n                        onChange={handleInputChange}\n                        placeholder=\"Name - Phone Number\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Blood Type</label>\n                      <select\n                        name=\"bloodType\"\n                        value={formData.bloodType}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      >\n                        <option value=\"\">Select Blood Type</option>\n                        <option value=\"A+\">A+</option>\n                        <option value=\"A-\">A-</option>\n                        <option value=\"B+\">B+</option>\n                        <option value=\"B-\">B-</option>\n                        <option value=\"AB+\">AB+</option>\n                        <option value=\"AB-\">AB-</option>\n                        <option value=\"O+\">O+</option>\n                        <option value=\"O-\">O-</option>\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Allergies</label>\n                      <textarea\n                        name=\"allergies\"\n                        value={formData.allergies}\n                        onChange={handleInputChange}\n                        rows=\"3\"\n                        placeholder=\"List any known allergies or 'None'\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Medical History</label>\n                      <textarea\n                        name=\"medicalHistory\"\n                        value={formData.medicalHistory}\n                        onChange={handleInputChange}\n                        rows=\"4\"\n                        placeholder=\"Previous conditions, surgeries, medications, etc.\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Form Actions */}\n                <div className=\"flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200\">\n                  <button\n                    type=\"button\"\n                    onClick={resetForm}\n                    className=\"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all duration-300 shadow-md hover:shadow-lg\"\n                  >\n                    {editingPatient ? 'Update Patient' : 'Add Patient'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Prescription Modal */}\n        {showPrescriptionModal && selectedPatientForPrescription && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col\">\n              <div className=\"bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 flex items-center justify-between flex-shrink-0\">\n                <h2 className=\"text-xl font-bold text-white\">💊 Create Prescription</h2>\n                <button\n                  onClick={resetPrescriptionForm}\n                  className=\"text-white hover:text-gray-200 transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              <div className=\"flex-1 overflow-y-auto p-6\">\n                <form onSubmit={handlePrescriptionSubmit} className=\"space-y-6\">\n                  {/* Patient Information */}\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Patient Information</h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700\">Name</p>\n                        <p className=\"text-gray-900\">{selectedPatientForPrescription.firstName} {selectedPatientForPrescription.lastName}</p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700\">National ID</p>\n                        <p className=\"text-gray-900\">{selectedPatientForPrescription.nationalId}</p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700\">Date of Birth</p>\n                        <p className=\"text-gray-900\">{new Date(selectedPatientForPrescription.dateOfBirth).toLocaleDateString()}</p>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700\">Blood Type</p>\n                        <p className=\"text-gray-900\">{selectedPatientForPrescription.bloodType || 'Not specified'}</p>\n                      </div>\n                    </div>\n                    {selectedPatientForPrescription.allergies && selectedPatientForPrescription.allergies !== 'None' && (\n                      <div className=\"mt-3\">\n                        <p className=\"text-sm font-medium text-red-700\">⚠️ Allergies</p>\n                        <p className=\"text-red-600 text-sm\">{selectedPatientForPrescription.allergies}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Prescription Details */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">Doctor Name *</label>\n                      <input\n                        type=\"text\"\n                        name=\"doctorName\"\n                        value={prescriptionData.doctorName}\n                        onChange={handlePrescriptionInputChange}\n                        required\n                        placeholder=\"Enter prescribing doctor's name\"\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">Prescription Date *</label>\n                      <input\n                        type=\"date\"\n                        name=\"prescriptionDate\"\n                        value={prescriptionData.prescriptionDate}\n                        onChange={handlePrescriptionInputChange}\n                        required\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Diagnosis *</label>\n                    <textarea\n                      name=\"diagnosis\"\n                      value={prescriptionData.diagnosis}\n                      onChange={handlePrescriptionInputChange}\n                      required\n                      rows=\"3\"\n                      placeholder=\"Enter patient's diagnosis...\"\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 resize-none\"\n                    />\n                  </div>\n\n                  {/* Medications */}\n                  <div>\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-lg font-semibold text-gray-900\">Medications</h3>\n                      <button\n                        type=\"button\"\n                        onClick={addMedication}\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors\"\n                      >\n                        ➕ Add Medication\n                      </button>\n                    </div>\n\n                    {prescriptionData.medications.map((medication, index) => (\n                      <div key={index} className=\"bg-gray-50 p-4 rounded-lg mb-4\">\n                        <div className=\"flex items-center justify-between mb-3\">\n                          <h4 className=\"font-medium text-gray-900\">Medication {index + 1}</h4>\n                          {prescriptionData.medications.length > 1 && (\n                            <button\n                              type=\"button\"\n                              onClick={() => removeMedication(index)}\n                              className=\"text-red-500 hover:text-red-700 text-sm\"\n                            >\n                              🗑️ Remove\n                            </button>\n                          )}\n                        </div>\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Medication Name *</label>\n                            <input\n                              type=\"text\"\n                              value={medication.name}\n                              onChange={(e) => handleMedicationChange(index, 'name', e.target.value)}\n                              required\n                              placeholder=\"e.g., Paracetamol\"\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                            />\n                          </div>\n\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Dosage *</label>\n                            <input\n                              type=\"text\"\n                              value={medication.dosage}\n                              onChange={(e) => handleMedicationChange(index, 'dosage', e.target.value)}\n                              required\n                              placeholder=\"e.g., 500mg\"\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                            />\n                          </div>\n\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Frequency *</label>\n                            <select\n                              value={medication.frequency}\n                              onChange={(e) => handleMedicationChange(index, 'frequency', e.target.value)}\n                              required\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                            >\n                              <option value=\"\">Select frequency</option>\n                              <option value=\"Once daily\">Once daily</option>\n                              <option value=\"Twice daily\">Twice daily</option>\n                              <option value=\"Three times daily\">Three times daily</option>\n                              <option value=\"Four times daily\">Four times daily</option>\n                              <option value=\"Every 4 hours\">Every 4 hours</option>\n                              <option value=\"Every 6 hours\">Every 6 hours</option>\n                              <option value=\"Every 8 hours\">Every 8 hours</option>\n                              <option value=\"As needed\">As needed</option>\n                            </select>\n                          </div>\n\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Duration *</label>\n                            <input\n                              type=\"text\"\n                              value={medication.duration}\n                              onChange={(e) => handleMedicationChange(index, 'duration', e.target.value)}\n                              required\n                              placeholder=\"e.g., 7 days\"\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                            />\n                          </div>\n                        </div>\n\n                        <div className=\"mt-4\">\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">Special Instructions</label>\n                          <textarea\n                            value={medication.instructions}\n                            onChange={(e) => handleMedicationChange(index, 'instructions', e.target.value)}\n                            rows=\"2\"\n                            placeholder=\"e.g., Take with food, avoid alcohol...\"\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 resize-none\"\n                          />\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Additional Notes */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Additional Notes</label>\n                    <textarea\n                      name=\"notes\"\n                      value={prescriptionData.notes}\n                      onChange={handlePrescriptionInputChange}\n                      rows=\"3\"\n                      placeholder=\"Any additional instructions or notes...\"\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 resize-none\"\n                    />\n                  </div>\n\n                  {/* Form Actions */}\n                  <div className=\"flex gap-4 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      onClick={resetPrescriptionForm}\n                      className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-all\"\n                    >\n                      Cancel\n                    </button>\n                    <button\n                      type=\"submit\"\n                      className=\"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:scale-105\"\n                    >\n                      Create Prescription\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default Patients;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACsB,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;;EAE1F;EACA,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC0B,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC;IACvDwC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,CACX;MACEC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE;IAChB,CAAC,CACF;IACDC,KAAK,EAAE,EAAE;IACTC,gBAAgB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACzD,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,2BAA2B;;EAEhD;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF9C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,WAAW,CAAC;MACxD,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBrD,WAAW,CAACmD,IAAI,CAACA,IAAI,CAAC;QACtBvC,mBAAmB,CAACuC,IAAI,CAACA,IAAI,CAAC;MAChC;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZlD,QAAQ,CAAC,oEAAoE,CAAC;MAC9EmD,OAAO,CAACpD,KAAK,CAAC,0BAA0B,EAAEmD,GAAG,CAAC;IAChD,CAAC,SAAS;MACRpD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAR,SAAS,CAAC,MAAM;IACdsD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtD,SAAS,CAAC,MAAM;IACd,IAAIe,WAAW,CAAC+C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B5C,mBAAmB,CAACb,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACL,MAAM0D,QAAQ,GAAG1D,QAAQ,CAAC2D,MAAM,CAACC,OAAO,IACtCA,OAAO,CAACxC,UAAU,CAACyC,QAAQ,CAACnD,WAAW,CAAC,IACxCkD,OAAO,CAACvC,SAAS,CAACyC,WAAW,CAAC,CAAC,CAACD,QAAQ,CAACnD,WAAW,CAACoD,WAAW,CAAC,CAAC,CAAC,IACnEF,OAAO,CAACtC,QAAQ,CAACwC,WAAW,CAAC,CAAC,CAACD,QAAQ,CAACnD,WAAW,CAACoD,WAAW,CAAC,CAAC,CAAC,IAClEF,OAAO,CAACrC,KAAK,CAACuC,WAAW,CAAC,CAAC,CAACD,QAAQ,CAACnD,WAAW,CAACoD,WAAW,CAAC,CAAC,CAAC,IAC/DF,OAAO,CAACpC,KAAK,CAACqC,QAAQ,CAACnD,WAAW,CACpC,CAAC;MACDG,mBAAmB,CAAC6C,QAAQ,CAAC;IAC/B;EACF,CAAC,EAAE,CAAChD,WAAW,EAAEV,QAAQ,CAAC,CAAC;;EAE3B;EACA,MAAM+D,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC/C,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC7B,IAAI,GAAG2B;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtBjD,WAAW,CAAC;MACVC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFtB,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM8D,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,IAAI9D,cAAc,EAAE;QAClB;QACA,MAAM0C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,aAAaxC,cAAc,CAACY,UAAU,EAAE,EAAE;UACpFmD,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACzD,QAAQ;QAC/B,CAAC,CAAC;QACF,MAAMkC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,MAAML,aAAa,CAAC,CAAC;UACrBmB,SAAS,CAAC,CAAC;UACX/D,QAAQ,CAAC,EAAE,CAAC;QACd,CAAC,MAAM;UACLA,QAAQ,CAAC+C,IAAI,CAACwB,OAAO,IAAI,0BAA0B,CAAC;QACtD;MACF,CAAC,MAAM;QACL;QACA,MAAM1B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,WAAW,EAAE;UACvDuB,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACzD,QAAQ;QAC/B,CAAC,CAAC;QACF,MAAMkC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,MAAML,aAAa,CAAC,CAAC;UACrBmB,SAAS,CAAC,CAAC;UACX/D,QAAQ,CAAC,EAAE,CAAC;QACd,CAAC,MAAM;UACLA,QAAQ,CAAC+C,IAAI,CAACwB,OAAO,IAAI,0BAA0B,CAAC;QACtD;MACF;IACF,CAAC,CAAC,OAAOrB,GAAG,EAAE;MACZlD,QAAQ,CAAC,uDAAuD,CAAC;MACjEmD,OAAO,CAACpD,KAAK,CAAC,uBAAuB,EAAEmD,GAAG,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAIjB,OAAO,IAAK;IAC9BzC,WAAW,CAAC;MACVC,UAAU,EAAEwC,OAAO,CAACxC,UAAU;MAC9BC,SAAS,EAAEuC,OAAO,CAACvC,SAAS;MAC5BC,QAAQ,EAAEsC,OAAO,CAACtC,QAAQ;MAC1BC,KAAK,EAAEqC,OAAO,CAACrC,KAAK;MACpBC,KAAK,EAAEoC,OAAO,CAACpC,KAAK;MACpBC,WAAW,EAAEmC,OAAO,CAACnC,WAAW;MAChCC,MAAM,EAAEkC,OAAO,CAAClC,MAAM;MACtBC,OAAO,EAAEiC,OAAO,CAACjC,OAAO;MACxBC,gBAAgB,EAAEgC,OAAO,CAAChC,gBAAgB;MAC1CC,SAAS,EAAE+B,OAAO,CAAC/B,SAAS;MAC5BC,SAAS,EAAE8B,OAAO,CAAC9B,SAAS;MAC5BC,cAAc,EAAE6B,OAAO,CAAC7B;IAC1B,CAAC,CAAC;IACFtB,iBAAiB,CAACmD,OAAO,CAAC;IAC1BrD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMuE,YAAY,GAAG,MAAO1D,UAAU,IAAK;IACzC,IAAI2D,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM9B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,aAAa5B,UAAU,EAAE,EAAE;UACrEmD,MAAM,EAAE;QACV,CAAC,CAAC;QACF,MAAMnB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,MAAML,aAAa,CAAC,CAAC;UACrB5C,QAAQ,CAAC,EAAE,CAAC;QACd,CAAC,MAAM;UACLA,QAAQ,CAAC+C,IAAI,CAACwB,OAAO,IAAI,0BAA0B,CAAC;QACtD;MACF,CAAC,CAAC,OAAOrB,GAAG,EAAE;QACZlD,QAAQ,CAAC,yDAAyD,CAAC;QACnEmD,OAAO,CAACpD,KAAK,CAAC,yBAAyB,EAAEmD,GAAG,CAAC;MAC/C;IACF;EACF,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAIrB,OAAO,IAAK;IACtC3C,iCAAiC,CAAC2C,OAAO,CAAC;IAC1C3B,mBAAmB,CAAC;MAClB,GAAGD,gBAAgB;MACnBE,SAAS,EAAE0B,OAAO,CAACxC,UAAU;MAC7BwB,gBAAgB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IACFhC,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMmE,6BAA6B,GAAIlB,CAAC,IAAK;IAC3C,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCjC,mBAAmB,CAACkC,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAC7B,IAAI,GAAG2B;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMkB,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEpB,KAAK,KAAK;IACtD,MAAMqB,kBAAkB,GAAG,CAAC,GAAGtD,gBAAgB,CAACK,WAAW,CAAC;IAC5DiD,kBAAkB,CAACF,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGpB,KAAK;IACxChC,mBAAmB,CAACkC,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP9B,WAAW,EAAEiD;IACf,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BtD,mBAAmB,CAACkC,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP9B,WAAW,EAAE,CACX,GAAG8B,IAAI,CAAC9B,WAAW,EACnB;QACEC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE;MAChB,CAAC;IAEL,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAIJ,KAAK,IAAK;IAClC,IAAIpD,gBAAgB,CAACK,WAAW,CAACoD,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMH,kBAAkB,GAAGtD,gBAAgB,CAACK,WAAW,CAACsB,MAAM,CAAC,CAAC+B,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKP,KAAK,CAAC;MACrFnD,mBAAmB,CAACkC,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP9B,WAAW,EAAEiD;MACf,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMM,qBAAqB,GAAGA,CAAA,KAAM;IAClC3D,mBAAmB,CAAC;MAClBC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,CACX;QACEC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE;MAChB,CAAC,CACF;MACDC,KAAK,EAAE,EAAE;MACTC,gBAAgB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IACF9B,iCAAiC,CAAC,IAAI,CAAC;IACvCF,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAM8E,wBAAwB,GAAG,MAAO7B,CAAC,IAAK;IAC5CA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBnE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM2F,mBAAmB,GAAG;QAC1B1E,UAAU,EAAEJ,8BAA8B,CAACI,UAAU;QACrD2E,QAAQ,EAAE,QAAQ;QAAE;QACpBnD,gBAAgB,EAAEZ,gBAAgB,CAACY,gBAAgB;QACnDR,SAAS,EAAEJ,gBAAgB,CAACI,SAAS;QACrCC,WAAW,EAAEL,gBAAgB,CAACK,WAAW,CAACsB,MAAM,CAACqC,GAAG,IAAIA,GAAG,CAAC1D,IAAI,CAACmB,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;QAC/Ed,KAAK,EAAEX,gBAAgB,CAACW,KAAK;QAC7BsD,UAAU,EAAE,IAAI;QAAE;QAClBC,SAAS,EAAElE,gBAAgB,CAACG;MAC9B,CAAC;MAED,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyC,EAAE;QACtEoB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmB,mBAAmB;MAC1C,CAAC,CAAC;MAEF,MAAMK,MAAM,GAAG,MAAMjD,QAAQ,CAACG,IAAI,CAAC,CAAC;MAEpC,IAAI8C,MAAM,CAAC7C,OAAO,EAAE;QAClB;QACA,IAAI8C,cAAc,GAAG,oCAAoC;QAEzD,IAAID,MAAM,CAAC/C,IAAI,CAACiD,eAAe,EAAE;UAC/B,IAAIF,MAAM,CAAC/C,IAAI,CAACiD,eAAe,CAACC,IAAI,EAAE;YACpCF,cAAc,IAAI,mCAAmCD,MAAM,CAAC/C,IAAI,CAACiD,eAAe,CAACE,EAAE,EAAE;UACvF,CAAC,MAAM;YACLH,cAAc,IAAI,mCAAmCD,MAAM,CAAC/C,IAAI,CAACiD,eAAe,CAACzB,OAAO,EAAE;UAC5F;QACF;QAEA4B,KAAK,CAACJ,cAAc,CAAC;QACrB5C,OAAO,CAACiD,GAAG,CAAC,uBAAuB,EAAEN,MAAM,CAAC/C,IAAI,CAAC;QACjDwC,qBAAqB,CAAC,CAAC;;QAEvB;QACA3C,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL,MAAM,IAAIyD,KAAK,CAACP,MAAM,CAACvB,OAAO,IAAI,+BAA+B,CAAC;MACpE;IACF,CAAC,CAAC,OAAOrB,GAAG,EAAE;MACZlD,QAAQ,CAAC,kDAAkD,CAAC;MAC5DmD,OAAO,CAACpD,KAAK,CAAC,8BAA8B,EAAEmD,GAAG,CAAC;MAClDiD,KAAK,CAAC,+BAA+B,GAAGjD,GAAG,CAACqB,OAAO,CAAC;IACtD,CAAC,SAAS;MACRzE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA;IAAK8G,SAAS,EAAC,gEAAgE;IAAAC,QAAA,eAC7E/G,OAAA;MAAK8G,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC/G,OAAA;QAAK8G,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/G,OAAA;UAAM8G,SAAS,EAAC,6HAA6H;UAAAC,QAAA,EAAC;QAE9I;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPnH,OAAA;UAAI8G,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzFnH,OAAA;UAAG8G,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGL5G,KAAK,iBACJP,OAAA;QAAK8G,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE/G,OAAA;UAAK8G,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/G,OAAA;YAAM8G,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CnH,OAAA;YAAG8G,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAExG;UAAK;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDnH,OAAA;QAAK8G,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E/G,OAAA;UAAK8G,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAE3E/G,OAAA;YAAK8G,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B/G,OAAA;cAAK8G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/G,OAAA;gBACEoH,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCjD,KAAK,EAAEvD,WAAY;gBACnByG,QAAQ,EAAGnD,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;gBAChD0C,SAAS,EAAC;cAAsH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjI,CAAC,eACFnH,OAAA;gBAAM8G,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnH,OAAA;YACEuH,OAAO,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;YACpCoG,SAAS,EAAC,2NAA2N;YAAAC,QAAA,EACtO;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnH,OAAA;UAAK8G,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD/G,OAAA;YAAK8G,SAAS,EAAC,uFAAuF;YAAAC,QAAA,gBACpG/G,OAAA;cAAK8G,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAE5G,QAAQ,CAACyF;YAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3EnH,OAAA;cAAK8G,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNnH,OAAA;YAAK8G,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAC9F/G,OAAA;cAAK8G,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEhG,gBAAgB,CAAC6E;YAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFnH,OAAA;cAAK8G,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNnH,OAAA;YAAK8G,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjG/G,OAAA;cAAK8G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC/C5G,QAAQ,CAAC2D,MAAM,CAAC0D,CAAC,IAAI,IAAIxE,IAAI,CAACwE,CAAC,CAACC,SAAS,CAAC,GAAG,IAAIzE,IAAI,CAACA,IAAI,CAAC0E,GAAG,CAAC,CAAC,GAAG,EAAE,GAAC,EAAE,GAAC,EAAE,GAAC,EAAE,GAAC,IAAI,CAAC,CAAC,CAAC9B;YAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC,eACNnH,OAAA;cAAK8G,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL9G,OAAO,gBACNL,OAAA;QAAK8G,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpF/G,OAAA;UAAK8G,SAAS,EAAC;QAAgG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtHnH,OAAA;UAAG8G,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,gBAENnH,OAAA;QAAK8G,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpF/G,OAAA;UAAK8G,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3C/G,OAAA;YAAI8G,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,EAELpG,gBAAgB,CAAC6E,MAAM,KAAK,CAAC,gBAC5B5F,OAAA;UAAK8G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/G,OAAA;YAAM8G,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CnH,OAAA;YAAI8G,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EnH,OAAA;YAAG8G,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzBlG,WAAW,GAAG,yCAAyC,GAAG;UAAqC;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENnH,OAAA;UAAK8G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B/G,OAAA;YAAO8G,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACvB/G,OAAA;cAAO8G,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B/G,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAI8G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3GnH,OAAA;kBAAI8G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/GnH,OAAA;kBAAI8G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3GnH,OAAA;kBAAI8G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3GnH,OAAA;kBAAI8G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChHnH,OAAA;kBAAI8G,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRnH,OAAA;cAAO8G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDhG,gBAAgB,CAAC4G,GAAG,CAAE5D,OAAO,iBAC5B/D,OAAA;gBAA6B8G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBACvD/G,OAAA;kBAAI8G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC/G,OAAA;oBAAK8G,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC/G,OAAA;sBAAK8G,SAAS,EAAC,4GAA4G;sBAAAC,QAAA,eACzH/G,OAAA;wBAAM8G,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,GACvChD,OAAO,CAACvC,SAAS,CAACoG,MAAM,CAAC,CAAC,CAAC,EAAE7D,OAAO,CAACtC,QAAQ,CAACmG,MAAM,CAAC,CAAC,CAAC;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNnH,OAAA;sBAAA+G,QAAA,gBACE/G,OAAA;wBAAK8G,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAC/ChD,OAAO,CAACvC,SAAS,EAAC,GAAC,EAACuC,OAAO,CAACtC,QAAQ;sBAAA;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACNnH,OAAA;wBAAK8G,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLnH,OAAA;kBAAI8G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBACzC/G,OAAA;oBAAK8G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEhD,OAAO,CAACxC;kBAAU;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7EnH,OAAA;oBAAK8G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACLnH,OAAA;kBAAI8G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBACzC/G,OAAA;oBAAK8G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEhD,OAAO,CAACrC;kBAAK;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DnH,OAAA;oBAAK8G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEhD,OAAO,CAACpC;kBAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACLnH,OAAA;kBAAI8G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBACzC/G,OAAA;oBAAK8G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACnChD,OAAO,CAAClC,MAAM,iBAAI7B,OAAA;sBAAM8G,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAAC,eAAG,EAAChD,OAAO,CAAClC,MAAM;oBAAA;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNnH,OAAA;oBAAK8G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,eAClC,EAAC,IAAI/D,IAAI,CAACe,OAAO,CAACnC,WAAW,CAAC,CAACiG,kBAAkB,CAAC,CAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLnH,OAAA;kBAAI8G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC/G,OAAA;oBACEuH,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACrB,OAAO,CAAE;oBAC3C+C,SAAS,EAAC,wNAAwN;oBAAAC,QAAA,EACnO;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLnH,OAAA;kBAAI8G,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,eAC7D/G,OAAA;oBAAK8G,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B/G,OAAA;sBACEuH,OAAO,EAAEA,CAAA,KAAMvC,UAAU,CAACjB,OAAO,CAAE;sBACnC+C,SAAS,EAAC,oFAAoF;sBAAAC,QAAA,EAC/F;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTnH,OAAA;sBACEuH,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAAClB,OAAO,CAACxC,UAAU,CAAE;sBAChDuF,SAAS,EAAC,iFAAiF;sBAAAC,QAAA,EAC5F;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAvDEpD,OAAO,CAACxC,UAAU;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwDvB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA1G,WAAW,iBACVT,OAAA;QAAK8G,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7F/G,OAAA;UAAK8G,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC5F/G,OAAA;YAAK8G,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C/G,OAAA;cAAK8G,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/G,OAAA;gBAAI8G,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC7CpG,cAAc,GAAG,iBAAiB,GAAG;cAAmB;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACLnH,OAAA;gBACEuH,OAAO,EAAEhD,SAAU;gBACnBuC,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnH,OAAA;YAAM8H,QAAQ,EAAEtD,YAAa;YAACsC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAC3C/G,OAAA;cAAK8G,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpD/G,OAAA;gBAAK8G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB/G,OAAA;kBAAI8G,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAErFnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrFnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,YAAY;oBACjB2B,KAAK,EAAE/C,QAAQ,CAACE,UAAW;oBAC3B+F,QAAQ,EAAEpD,iBAAkB;oBAC5B6D,QAAQ;oBACRC,QAAQ,EAAErH,cAAc,GAAG,IAAI,GAAG,KAAM;oBACxC0G,WAAW,EAAC,+BAA+B;oBAC3CY,OAAO,EAAC,cAAc;oBACtBC,KAAK,EAAC,kCAAkC;oBACxCpB,SAAS,EAAE,kHAAkHnG,cAAc,GAAG,gCAAgC,GAAG,EAAE;kBAAG;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvL,CAAC,EACDxG,cAAc,iBACbX,OAAA;oBAAG8G,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAC3E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,WAAW;oBAChB2B,KAAK,EAAE/C,QAAQ,CAACG,SAAU;oBAC1B8F,QAAQ,EAAEpD,iBAAkB;oBAC5B6D,QAAQ;oBACRjB,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnFnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,UAAU;oBACf2B,KAAK,EAAE/C,QAAQ,CAACI,QAAS;oBACzB6F,QAAQ,EAAEpD,iBAAkB;oBAC5B6D,QAAQ;oBACRjB,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EnH,OAAA;oBACEoH,IAAI,EAAC,OAAO;oBACZ3E,IAAI,EAAC,OAAO;oBACZ2B,KAAK,EAAE/C,QAAQ,CAACK,KAAM;oBACtB4F,QAAQ,EAAEpD,iBAAkB;oBAC5B6D,QAAQ;oBACRjB,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EnH,OAAA;oBACEoH,IAAI,EAAC,KAAK;oBACV3E,IAAI,EAAC,OAAO;oBACZ2B,KAAK,EAAE/C,QAAQ,CAACM,KAAM;oBACtB2F,QAAQ,EAAEpD,iBAAkB;oBAC5B6D,QAAQ;oBACRjB,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvFnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,aAAa;oBAClB2B,KAAK,EAAE/C,QAAQ,CAACO,WAAY;oBAC5B0F,QAAQ,EAAEpD,iBAAkB;oBAC5B6D,QAAQ;oBACRjB,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9EnH,OAAA;oBACEyC,IAAI,EAAC,QAAQ;oBACb2B,KAAK,EAAE/C,QAAQ,CAACQ,MAAO;oBACvByF,QAAQ,EAAEpD,iBAAkB;oBAC5B4C,SAAS,EAAC,gHAAgH;oBAAAC,QAAA,gBAE1H/G,OAAA;sBAAQoE,KAAK,EAAC,EAAE;sBAAA2C,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvCnH,OAAA;sBAAQoE,KAAK,EAAC,MAAM;sBAAA2C,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClCnH,OAAA;sBAAQoE,KAAK,EAAC,QAAQ;sBAAA2C,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCnH,OAAA;sBAAQoE,KAAK,EAAC,OAAO;sBAAA2C,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EnH,OAAA;oBACEyC,IAAI,EAAC,SAAS;oBACd2B,KAAK,EAAE/C,QAAQ,CAACS,OAAQ;oBACxBwF,QAAQ,EAAEpD,iBAAkB;oBAC5BiE,IAAI,EAAC,GAAG;oBACRrB,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnH,OAAA;gBAAK8G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB/G,OAAA;kBAAI8G,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEpFnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzFnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,kBAAkB;oBACvB2B,KAAK,EAAE/C,QAAQ,CAACU,gBAAiB;oBACjCuF,QAAQ,EAAEpD,iBAAkB;oBAC5BmD,WAAW,EAAC,qBAAqB;oBACjCP,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClFnH,OAAA;oBACEyC,IAAI,EAAC,WAAW;oBAChB2B,KAAK,EAAE/C,QAAQ,CAACW,SAAU;oBAC1BsF,QAAQ,EAAEpD,iBAAkB;oBAC5B4C,SAAS,EAAC,gHAAgH;oBAAAC,QAAA,gBAE1H/G,OAAA;sBAAQoE,KAAK,EAAC,EAAE;sBAAA2C,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3CnH,OAAA;sBAAQoE,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BnH,OAAA;sBAAQoE,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BnH,OAAA;sBAAQoE,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BnH,OAAA;sBAAQoE,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BnH,OAAA;sBAAQoE,KAAK,EAAC,KAAK;sBAAA2C,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChCnH,OAAA;sBAAQoE,KAAK,EAAC,KAAK;sBAAA2C,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChCnH,OAAA;sBAAQoE,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9BnH,OAAA;sBAAQoE,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjFnH,OAAA;oBACEyC,IAAI,EAAC,WAAW;oBAChB2B,KAAK,EAAE/C,QAAQ,CAACY,SAAU;oBAC1BqF,QAAQ,EAAEpD,iBAAkB;oBAC5BiE,IAAI,EAAC,GAAG;oBACRd,WAAW,EAAC,oCAAoC;oBAChDP,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvFnH,OAAA;oBACEyC,IAAI,EAAC,gBAAgB;oBACrB2B,KAAK,EAAE/C,QAAQ,CAACa,cAAe;oBAC/BoF,QAAQ,EAAEpD,iBAAkB;oBAC5BiE,IAAI,EAAC,GAAG;oBACRd,WAAW,EAAC,mDAAmD;oBAC/DP,SAAS,EAAC;kBAAgH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnH,OAAA;cAAK8G,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAC5E/G,OAAA;gBACEoH,IAAI,EAAC,QAAQ;gBACbG,OAAO,EAAEhD,SAAU;gBACnBuC,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,EACzG;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnH,OAAA;gBACEoH,IAAI,EAAC,QAAQ;gBACbN,SAAS,EAAC,4KAA4K;gBAAAC,QAAA,EAErLpG,cAAc,GAAG,gBAAgB,GAAG;cAAa;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlG,qBAAqB,IAAIE,8BAA8B,iBACtDnB,OAAA;QAAK8G,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7F/G,OAAA;UAAK8G,SAAS,EAAC,6FAA6F;UAAAC,QAAA,gBAC1G/G,OAAA;YAAK8G,SAAS,EAAC,uGAAuG;YAAAC,QAAA,gBACpH/G,OAAA;cAAI8G,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEnH,OAAA;cACEuH,OAAO,EAAExB,qBAAsB;cAC/Be,SAAS,EAAC,kDAAkD;cAAAC,QAAA,eAE5D/G,OAAA;gBAAK8G,SAAS,EAAC,SAAS;gBAACsB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAxB,QAAA,eAC5F/G,OAAA;kBAAMwI,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAsB;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnH,OAAA;YAAK8G,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC/G,OAAA;cAAM8H,QAAQ,EAAE9B,wBAAyB;cAACc,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAE7D/G,OAAA;gBAAK8G,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC/G,OAAA;kBAAI8G,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjFnH,OAAA;kBAAK8G,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD/G,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAG8G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzDnH,OAAA;sBAAG8G,SAAS,EAAC,eAAe;sBAAAC,QAAA,GAAE5F,8BAA8B,CAACK,SAAS,EAAC,GAAC,EAACL,8BAA8B,CAACM,QAAQ;oBAAA;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClH,CAAC,eACNnH,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAG8G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAChEnH,OAAA;sBAAG8G,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAE5F,8BAA8B,CAACI;oBAAU;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACNnH,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAG8G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClEnH,OAAA;sBAAG8G,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAE,IAAI/D,IAAI,CAAC7B,8BAA8B,CAACS,WAAW,CAAC,CAACiG,kBAAkB,CAAC;oBAAC;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC,eACNnH,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAG8G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC/DnH,OAAA;sBAAG8G,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAE5F,8BAA8B,CAACa,SAAS,IAAI;oBAAe;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLhG,8BAA8B,CAACc,SAAS,IAAId,8BAA8B,CAACc,SAAS,KAAK,MAAM,iBAC9FjC,OAAA;kBAAK8G,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB/G,OAAA;oBAAG8G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChEnH,OAAA;oBAAG8G,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAE5F,8BAA8B,CAACc;kBAAS;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNnH,OAAA;gBAAK8G,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD/G,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrFnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,YAAY;oBACjB2B,KAAK,EAAEjC,gBAAgB,CAACG,UAAW;oBACnCgF,QAAQ,EAAEjC,6BAA8B;oBACxC0C,QAAQ;oBACRV,WAAW,EAAC,iCAAiC;oBAC7CP,SAAS,EAAC;kBAAyG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnH,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAO8G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3FnH,OAAA;oBACEoH,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,kBAAkB;oBACvB2B,KAAK,EAAEjC,gBAAgB,CAACY,gBAAiB;oBACzCuE,QAAQ,EAAEjC,6BAA8B;oBACxC0C,QAAQ;oBACRjB,SAAS,EAAC;kBAAyG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnH,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAO8G,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnFnH,OAAA;kBACEyC,IAAI,EAAC,WAAW;kBAChB2B,KAAK,EAAEjC,gBAAgB,CAACI,SAAU;kBAClC+E,QAAQ,EAAEjC,6BAA8B;kBACxC0C,QAAQ;kBACRI,IAAI,EAAC,GAAG;kBACRd,WAAW,EAAC,8BAA8B;kBAC1CP,SAAS,EAAC;gBAAqH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNnH,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAK8G,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/G,OAAA;oBAAI8G,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpEnH,OAAA;oBACEoH,IAAI,EAAC,QAAQ;oBACbG,OAAO,EAAE7B,aAAc;oBACvBoB,SAAS,EAAC,yFAAyF;oBAAAC,QAAA,EACpG;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAELhF,gBAAgB,CAACK,WAAW,CAACmF,GAAG,CAAC,CAACgB,UAAU,EAAEpD,KAAK,kBAClDvF,OAAA;kBAAiB8G,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBACzD/G,OAAA;oBAAK8G,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/G,OAAA;sBAAI8G,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GAAC,aAAW,EAACxB,KAAK,GAAG,CAAC;oBAAA;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACpEhF,gBAAgB,CAACK,WAAW,CAACoD,MAAM,GAAG,CAAC,iBACtC5F,OAAA;sBACEoH,IAAI,EAAC,QAAQ;sBACbG,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACJ,KAAK,CAAE;sBACvCuB,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EACpD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENnH,OAAA;oBAAK8G,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD/G,OAAA;sBAAA+G,QAAA,gBACE/G,OAAA;wBAAO8G,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzFnH,OAAA;wBACEoH,IAAI,EAAC,MAAM;wBACXhD,KAAK,EAAEuE,UAAU,CAAClG,IAAK;wBACvB6E,QAAQ,EAAGnD,CAAC,IAAKmB,sBAAsB,CAACC,KAAK,EAAE,MAAM,EAAEpB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;wBACvE2D,QAAQ;wBACRV,WAAW,EAAC,mBAAmB;wBAC/BP,SAAS,EAAC;sBAAyG;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAENnH,OAAA;sBAAA+G,QAAA,gBACE/G,OAAA;wBAAO8G,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAChFnH,OAAA;wBACEoH,IAAI,EAAC,MAAM;wBACXhD,KAAK,EAAEuE,UAAU,CAACjG,MAAO;wBACzB4E,QAAQ,EAAGnD,CAAC,IAAKmB,sBAAsB,CAACC,KAAK,EAAE,QAAQ,EAAEpB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;wBACzE2D,QAAQ;wBACRV,WAAW,EAAC,aAAa;wBACzBP,SAAS,EAAC;sBAAyG;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAENnH,OAAA;sBAAA+G,QAAA,gBACE/G,OAAA;wBAAO8G,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACnFnH,OAAA;wBACEoE,KAAK,EAAEuE,UAAU,CAAChG,SAAU;wBAC5B2E,QAAQ,EAAGnD,CAAC,IAAKmB,sBAAsB,CAACC,KAAK,EAAE,WAAW,EAAEpB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;wBAC5E2D,QAAQ;wBACRjB,SAAS,EAAC,yGAAyG;wBAAAC,QAAA,gBAEnH/G,OAAA;0BAAQoE,KAAK,EAAC,EAAE;0BAAA2C,QAAA,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1CnH,OAAA;0BAAQoE,KAAK,EAAC,YAAY;0BAAA2C,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC9CnH,OAAA;0BAAQoE,KAAK,EAAC,aAAa;0BAAA2C,QAAA,EAAC;wBAAW;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAChDnH,OAAA;0BAAQoE,KAAK,EAAC,mBAAmB;0BAAA2C,QAAA,EAAC;wBAAiB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5DnH,OAAA;0BAAQoE,KAAK,EAAC,kBAAkB;0BAAA2C,QAAA,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1DnH,OAAA;0BAAQoE,KAAK,EAAC,eAAe;0BAAA2C,QAAA,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpDnH,OAAA;0BAAQoE,KAAK,EAAC,eAAe;0BAAA2C,QAAA,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpDnH,OAAA;0BAAQoE,KAAK,EAAC,eAAe;0BAAA2C,QAAA,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpDnH,OAAA;0BAAQoE,KAAK,EAAC,WAAW;0BAAA2C,QAAA,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENnH,OAAA;sBAAA+G,QAAA,gBACE/G,OAAA;wBAAO8G,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClFnH,OAAA;wBACEoH,IAAI,EAAC,MAAM;wBACXhD,KAAK,EAAEuE,UAAU,CAAC/F,QAAS;wBAC3B0E,QAAQ,EAAGnD,CAAC,IAAKmB,sBAAsB,CAACC,KAAK,EAAE,UAAU,EAAEpB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;wBAC3E2D,QAAQ;wBACRV,WAAW,EAAC,cAAc;wBAC1BP,SAAS,EAAC;sBAAyG;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnH,OAAA;oBAAK8G,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB/G,OAAA;sBAAO8G,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5FnH,OAAA;sBACEoE,KAAK,EAAEuE,UAAU,CAAC9F,YAAa;sBAC/ByE,QAAQ,EAAGnD,CAAC,IAAKmB,sBAAsB,CAACC,KAAK,EAAE,cAAc,EAAEpB,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;sBAC/E+D,IAAI,EAAC,GAAG;sBACRd,WAAW,EAAC,wCAAwC;sBACpDP,SAAS,EAAC;oBAAqH;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAjFE5B,KAAK;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkFV,CACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNnH,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAO8G,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFnH,OAAA;kBACEyC,IAAI,EAAC,OAAO;kBACZ2B,KAAK,EAAEjC,gBAAgB,CAACW,KAAM;kBAC9BwE,QAAQ,EAAEjC,6BAA8B;kBACxC8C,IAAI,EAAC,GAAG;kBACRd,WAAW,EAAC,yCAAyC;kBACrDP,SAAS,EAAC;gBAAqH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNnH,OAAA;gBAAK8G,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvD/G,OAAA;kBACEoH,IAAI,EAAC,QAAQ;kBACbG,OAAO,EAAExB,qBAAsB;kBAC/Be,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,EACjH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnH,OAAA;kBACEoH,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,wMAAwM;kBAAAC,QAAA,EACnN;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjH,EAAA,CAl7BQD,QAAQ;AAAA2I,EAAA,GAAR3I,QAAQ;AAo7BjB,eAAeA,QAAQ;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}