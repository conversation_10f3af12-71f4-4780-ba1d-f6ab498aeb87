{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Healthcare\\\\frontend\\\\src\\\\context\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const hasCheckedAuth = useRef(false);\n\n  // Remove session timeout - sessions should only end when browser closes\n  // const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // Disabled\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      // Prevent double authentication checks (React StrictMode issue)\n      if (hasCheckedAuth.current) {\n        console.log('🔄 Authentication already checked, skipping...');\n        return;\n      }\n      hasCheckedAuth.current = true;\n      console.log('🔍 Checking authentication on app load...');\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        const loginTime = sessionStorage.getItem('loginTime');\n        console.log('📦 SessionStorage contents:', {\n          token: token ? 'EXISTS' : 'MISSING',\n          userData: userData ? 'EXISTS' : 'MISSING',\n          loginTime: loginTime ? 'EXISTS' : 'MISSING'\n        });\n        if (token && userData && loginTime) {\n          // No timeout check - session persists until browser closes\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          console.log('✅ Session restored from sessionStorage:', parsedUser.name);\n          console.log('👤 User role:', parsedUser.role);\n        } else {\n          console.log('❌ No valid session found - missing required data');\n          console.log('🔍 Detailed check:', {\n            hasToken: !!token,\n            hasUserData: !!userData,\n            hasLoginTime: !!loginTime\n          });\n          setUser(null);\n        }\n      } catch (error) {\n        console.error('💥 Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n        sessionStorage.removeItem('loginTime');\n        setUser(null);\n      } finally {\n        setLoading(false);\n        console.log('🏁 Authentication check completed');\n      }\n    };\n    checkAuth();\n\n    // Session management - sessionStorage automatically clears when browser/tab closes\n    // sessionStorage will persist across page refreshes but clear when tab/browser closes\n\n    // Add debugging to track session behavior\n    const handleBeforeUnload = event => {\n      // This fires on both refresh and browser close\n      // We don't want to clear session on refresh, only on browser close\n      // sessionStorage will handle this automatically, but let's log for debugging\n      console.log('Page unloading - session will persist if it\\'s a refresh, clear if browser closes');\n    };\n\n    // Add event listener for debugging\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  // Login function\n  const login = userData => {\n    console.log('🔐 Login function called with:', userData);\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    const loginTime = new Date().getTime();\n    const token = userData.token || 'authenticated';\n    sessionStorage.setItem('authToken', token);\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    sessionStorage.setItem('loginTime', loginTime.toString());\n    console.log('✅ User logged in and session stored:', userData.name);\n    console.log('📦 Stored in sessionStorage:', {\n      token: token,\n      userData: 'JSON string stored',\n      loginTime: loginTime\n    });\n    console.log('🔄 Session will persist until browser/tab is closed');\n\n    // Verify storage immediately\n    const storedToken = sessionStorage.getItem('authToken');\n    const storedUserData = sessionStorage.getItem('userData');\n    const storedLoginTime = sessionStorage.getItem('loginTime');\n    console.log('🔍 Verification - Data actually stored:', {\n      token: storedToken ? 'YES' : 'NO',\n      userData: storedUserData ? 'YES' : 'NO',\n      loginTime: storedLoginTime ? 'YES' : 'NO'\n    });\n  };\n\n  // Logout function\n  const logout = redirectCallback => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    sessionStorage.removeItem('loginTime');\n    setUser(null);\n    console.log('User logged out and session cleared');\n\n    // Call redirect callback if provided\n    if (redirectCallback && typeof redirectCallback === 'function') {\n      redirectCallback();\n    }\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = role => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"kuW1lmOt+Ez7zoPOaINRAhNfwxI=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "hasCheckedAuth", "checkAuth", "current", "console", "log", "token", "sessionStorage", "getItem", "userData", "loginTime", "parsedUser", "JSON", "parse", "name", "role", "hasToken", "hasUserData", "hasLoginTime", "error", "removeItem", "handleBeforeUnload", "event", "window", "addEventListener", "removeEventListener", "login", "Date", "getTime", "setItem", "stringify", "toString", "storedToken", "storedUserData", "storedLoginTime", "logout", "redirectCallback", "isAuthenticated", "hasRole", "isAdmin", "userType", "isStaff", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Healthcare/frontend/src/context/AuthContext.jsx"], "sourcesContent": ["import { createContext, useContext, useState, useEffect, useRef } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const hasCheckedAuth = useRef(false);\n\n  // Remove session timeout - sessions should only end when browser closes\n  // const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // Disabled\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      // Prevent double authentication checks (React StrictMode issue)\n      if (hasCheckedAuth.current) {\n        console.log('🔄 Authentication already checked, skipping...');\n        return;\n      }\n\n      hasCheckedAuth.current = true;\n      console.log('🔍 Checking authentication on app load...');\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        const loginTime = sessionStorage.getItem('loginTime');\n\n        console.log('📦 SessionStorage contents:', {\n          token: token ? 'EXISTS' : 'MISSING',\n          userData: userData ? 'EXISTS' : 'MISSING',\n          loginTime: loginTime ? 'EXISTS' : 'MISSING'\n        });\n\n        if (token && userData && loginTime) {\n          // No timeout check - session persists until browser closes\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          console.log('✅ Session restored from sessionStorage:', parsedUser.name);\n          console.log('👤 User role:', parsedUser.role);\n        } else {\n          console.log('❌ No valid session found - missing required data');\n          console.log('🔍 Detailed check:', {\n            hasToken: !!token,\n            hasUserData: !!userData,\n            hasLoginTime: !!loginTime\n          });\n          setUser(null);\n        }\n      } catch (error) {\n        console.error('💥 Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n        sessionStorage.removeItem('loginTime');\n        setUser(null);\n      } finally {\n        setLoading(false);\n        console.log('🏁 Authentication check completed');\n      }\n    };\n\n    checkAuth();\n\n    // Session management - sessionStorage automatically clears when browser/tab closes\n    // sessionStorage will persist across page refreshes but clear when tab/browser closes\n\n    // Add debugging to track session behavior\n    const handleBeforeUnload = (event) => {\n      // This fires on both refresh and browser close\n      // We don't want to clear session on refresh, only on browser close\n      // sessionStorage will handle this automatically, but let's log for debugging\n      console.log('Page unloading - session will persist if it\\'s a refresh, clear if browser closes');\n    };\n\n    // Add event listener for debugging\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  // Login function\n  const login = (userData) => {\n    console.log('🔐 Login function called with:', userData);\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    const loginTime = new Date().getTime();\n    const token = userData.token || 'authenticated';\n\n    sessionStorage.setItem('authToken', token);\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    sessionStorage.setItem('loginTime', loginTime.toString());\n\n    console.log('✅ User logged in and session stored:', userData.name);\n    console.log('📦 Stored in sessionStorage:', {\n      token: token,\n      userData: 'JSON string stored',\n      loginTime: loginTime\n    });\n    console.log('🔄 Session will persist until browser/tab is closed');\n\n    // Verify storage immediately\n    const storedToken = sessionStorage.getItem('authToken');\n    const storedUserData = sessionStorage.getItem('userData');\n    const storedLoginTime = sessionStorage.getItem('loginTime');\n    console.log('🔍 Verification - Data actually stored:', {\n      token: storedToken ? 'YES' : 'NO',\n      userData: storedUserData ? 'YES' : 'NO',\n      loginTime: storedLoginTime ? 'YES' : 'NO'\n    });\n  };\n\n  // Logout function\n  const logout = (redirectCallback) => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    sessionStorage.removeItem('loginTime');\n    setUser(null);\n    console.log('User logged out and session cleared');\n\n    // Call redirect callback if provided\n    if (redirectCallback && typeof redirectCallback === 'function') {\n      redirectCallback();\n    }\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = (role) => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMiB,cAAc,GAAGf,MAAM,CAAC,KAAK,CAAC;;EAEpC;EACA;;EAEA;EACAD,SAAS,CAAC,MAAM;IACd,MAAMiB,SAAS,GAAGA,CAAA,KAAM;MACtB;MACA,IAAID,cAAc,CAACE,OAAO,EAAE;QAC1BC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D;MACF;MAEAJ,cAAc,CAACE,OAAO,GAAG,IAAI;MAC7BC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI;QACF;QACA;QACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QACjD,MAAMC,QAAQ,GAAGF,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC;QACnD,MAAME,SAAS,GAAGH,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QAErDJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;UACzCC,KAAK,EAAEA,KAAK,GAAG,QAAQ,GAAG,SAAS;UACnCG,QAAQ,EAAEA,QAAQ,GAAG,QAAQ,GAAG,SAAS;UACzCC,SAAS,EAAEA,SAAS,GAAG,QAAQ,GAAG;QACpC,CAAC,CAAC;QAEF,IAAIJ,KAAK,IAAIG,QAAQ,IAAIC,SAAS,EAAE;UAClC;UACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UACvCX,OAAO,CAACa,UAAU,CAAC;UACnBP,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEM,UAAU,CAACG,IAAI,CAAC;UACvEV,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,UAAU,CAACI,IAAI,CAAC;QAC/C,CAAC,MAAM;UACLX,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;UAC/DD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChCW,QAAQ,EAAE,CAAC,CAACV,KAAK;YACjBW,WAAW,EAAE,CAAC,CAACR,QAAQ;YACvBS,YAAY,EAAE,CAAC,CAACR;UAClB,CAAC,CAAC;UACFZ,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD;QACAZ,cAAc,CAACa,UAAU,CAAC,WAAW,CAAC;QACtCb,cAAc,CAACa,UAAU,CAAC,UAAU,CAAC;QACrCb,cAAc,CAACa,UAAU,CAAC,WAAW,CAAC;QACtCtB,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;QACjBI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAClD;IACF,CAAC;IAEDH,SAAS,CAAC,CAAC;;IAEX;IACA;;IAEA;IACA,MAAMmB,kBAAkB,GAAIC,KAAK,IAAK;MACpC;MACA;MACA;MACAlB,OAAO,CAACC,GAAG,CAAC,mFAAmF,CAAC;IAClG,CAAC;;IAED;IACAkB,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;;IAE3D;IACA,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEJ,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,KAAK,GAAIjB,QAAQ,IAAK;IAC1BL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,QAAQ,CAAC;IACvDX,OAAO,CAACW,QAAQ,CAAC;IACjB;IACA,MAAMC,SAAS,GAAG,IAAIiB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtC,MAAMtB,KAAK,GAAGG,QAAQ,CAACH,KAAK,IAAI,eAAe;IAE/CC,cAAc,CAACsB,OAAO,CAAC,WAAW,EAAEvB,KAAK,CAAC;IAC1CC,cAAc,CAACsB,OAAO,CAAC,UAAU,EAAEjB,IAAI,CAACkB,SAAS,CAACrB,QAAQ,CAAC,CAAC;IAC5DF,cAAc,CAACsB,OAAO,CAAC,WAAW,EAAEnB,SAAS,CAACqB,QAAQ,CAAC,CAAC,CAAC;IAEzD3B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEI,QAAQ,CAACK,IAAI,CAAC;IAClEV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CC,KAAK,EAAEA,KAAK;MACZG,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAEA;IACb,CAAC,CAAC;IACFN,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;IAElE;IACA,MAAM2B,WAAW,GAAGzB,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACvD,MAAMyB,cAAc,GAAG1B,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC;IACzD,MAAM0B,eAAe,GAAG3B,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IAC3DJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDC,KAAK,EAAE0B,WAAW,GAAG,KAAK,GAAG,IAAI;MACjCvB,QAAQ,EAAEwB,cAAc,GAAG,KAAK,GAAG,IAAI;MACvCvB,SAAS,EAAEwB,eAAe,GAAG,KAAK,GAAG;IACvC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,MAAM,GAAIC,gBAAgB,IAAK;IACnC;IACA7B,cAAc,CAACa,UAAU,CAAC,WAAW,CAAC;IACtCb,cAAc,CAACa,UAAU,CAAC,UAAU,CAAC;IACrCb,cAAc,CAACa,UAAU,CAAC,WAAW,CAAC;IACtCtB,OAAO,CAAC,IAAI,CAAC;IACbM,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAElD;IACA,IAAI+B,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC9DA,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAACxC,IAAI;EACf,CAAC;;EAED;EACA,MAAMyC,OAAO,GAAIvB,IAAI,IAAK;IACxB,OAAOlB,IAAI,IAAIA,IAAI,CAACkB,IAAI,KAAKA,IAAI;EACnC,CAAC;;EAED;EACA,MAAMwB,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAO1C,IAAI,KAAKA,IAAI,CAACkB,IAAI,KAAK,OAAO,IAAIlB,IAAI,CAAC2C,QAAQ,KAAK,OAAO,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAO5C,IAAI,KAAKA,IAAI,CAACkB,IAAI,KAAK,QAAQ,IAAIlB,IAAI,CAACkB,IAAI,KAAK,OAAO,IAAIlB,IAAI,CAAC2C,QAAQ,KAAK,OAAO,CAAC;EAC/F,CAAC;EAED,MAAME,KAAK,GAAG;IACZ7C,IAAI;IACJ6B,KAAK;IACLS,MAAM;IACNE,eAAe;IACfC,OAAO;IACPC,OAAO;IACPE,OAAO;IACP1C;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAACsD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/C,QAAA,EAChCA;EAAQ;IAAAiD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnD,GAAA,CApKWF,YAAY;AAAAsD,EAAA,GAAZtD,YAAY;AAsKzB,eAAeL,WAAW;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}