{"ast": null, "code": "var _jsxFileName = \"B:\\\\Done projects\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\Help.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Help() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-20\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-semibold mb-4\",\n          children: \"\\uD83D\\uDCA1 Support Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n          children: \"Help Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Find answers to common questions and get the support you need for our healthcare platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 6,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-xl\",\n              children: \"\\u2753\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border-l-4 border-blue-500 hover:shadow-lg transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-4 mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm\",\n                    children: \"\\uD83D\\uDCC5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 30,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                    children: \"How do I schedule an appointment?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 33,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: \"To schedule an appointment, navigate to the Appointments section from your dashboard, click on \\\"New Appointment,\\\" select your preferred healthcare provider, date, and time. You'll receive a confirmation email with all the details.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 34,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border-l-4 border-green-500 hover:shadow-lg transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm\",\n                    children: \"\\uD83D\\uDCCB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                    children: \"How can I access my medical records?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: \"Your medical records can be accessed from the Medical Records section. You can view, download, or share your records with healthcare providers as needed. All records are organized by date and medical specialty for easy navigation.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border-l-4 border-purple-500 hover:shadow-lg transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-4 mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm\",\n                    children: \"\\uD83D\\uDD12\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                    children: \"Is my information secure?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 69,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: \"Yes, we take data security very seriously. All your information is encrypted and stored securely in compliance with HIPAA and other healthcare privacy regulations. We use industry-standard security measures to protect your data.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 70,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-orange-50 to-orange-100 p-6 rounded-xl border-l-4 border-orange-500 hover:shadow-lg transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4 mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm\",\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                    children: \"How do I contact my doctor?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: \"You can contact your doctor through the Messages section. Simply select your healthcare provider and send a secure message. For urgent matters, please call 112 for emergency or visit the nearest emergency room.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-teal-50 to-teal-100 p-6 rounded-xl border-l-4 border-teal-500 hover:shadow-lg transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center mr-4 mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm\",\n                    children: \"\\uD83D\\uDC64\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                    children: \"How do I update my personal information?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: \"To update your personal information, go to your Profile settings. You can update your contact information, insurance details, and emergency contacts. Some changes may require verification from our support team.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 gap-8 mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-xl\",\n                  children: \"\\uD83C\\uDFA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-blue-900\",\n                children: \"Need More Help?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-800 mb-6 leading-relaxed\",\n              children: \"Our support team is available 24/7 to assist you with any questions or issues. Get personalized help from our healthcare technology experts.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\",\n                children: \"Contact Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full bg-white hover:bg-blue-50 text-blue-600 font-semibold py-3 px-6 rounded-xl border-2 border-blue-300 hover:border-blue-400 transition-all duration-300\",\n                children: \"View Documentation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-8 border border-red-200 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mr-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-xl\",\n                  children: \"\\uD83D\\uDEA8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-red-900\",\n                children: \"Emergency Contacts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center p-3 bg-white rounded-lg border border-red-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 mr-3 text-lg\",\n                  children: \"\\uD83D\\uDE91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-semibold text-red-800\",\n                    children: \"Emergency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-700 text-lg font-bold\",\n                    children: \"112\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center p-3 bg-white rounded-lg border border-red-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 mr-3 text-lg\",\n                  children: \"\\uD83C\\uDFE5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-semibold text-red-800\",\n                    children: \"Hospital\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-700\",\n                    children: \"(555) 123-4567\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center p-3 bg-white rounded-lg border border-red-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 mr-3 text-lg\",\n                  children: \"\\u2622\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-semibold text-red-800\",\n                    children: \"Poison Control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-700\",\n                    children: \"1-************\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center p-3 bg-white rounded-lg border border-red-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 mr-3 text-lg\",\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-semibold text-red-800\",\n                    children: \"Mental Health Crisis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-700 text-lg font-bold\",\n                    children: \"988\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-xl\",\n              children: \"\\uD83D\\uDD17\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105 border border-indigo-200 text-left w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white\",\n                    children: \"\\uD83D\\uDCD6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-indigo-900 text-lg\",\n                  children: \"User Guide\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-indigo-700 leading-relaxed\",\n                children: \"Complete guide to using the platform with detailed instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-pink-50 to-pink-100 p-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105 border border-pink-200 text-left w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white\",\n                    children: \"\\uD83C\\uDFA5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-pink-900 text-lg\",\n                  children: \"Video Tutorials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-pink-700 leading-relaxed\",\n                children: \"Step-by-step video instructions for all platform features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-emerald-50 to-emerald-100 p-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105 border border-emerald-200 text-left w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-emerald-500 rounded-lg flex items-center justify-center mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white\",\n                    children: \"\\uD83D\\uDEE1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-emerald-900 text-lg\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-emerald-700 leading-relaxed\",\n                children: \"How we protect your information and ensure data security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6 mt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-1 md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"HEALTHCARE Portal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300 mb-6 max-w-md leading-relaxed\",\n              children: \"Your trusted healthcare management platform providing comprehensive digital solutions for modern medical care and patient management.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"\\uD83D\\uDCE7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"\\uD83D\\uDCF1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"\\uD83C\\uDF10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Quick Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"About Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Doctors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Contact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Help Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Terms of Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Emergency: 112\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-700 pt-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"\\xA9 2025 HealthCarePro. All rights reserved. | Terms & Privacy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 5\n  }, this);\n}\n_c = Help;\nexport default Help;\nvar _c;\n$RefreshReg$(_c, \"Help\");", "map": {"version": 3, "names": ["Help", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "fillRule", "d", "clipRule", "href", "_c", "$RefreshReg$"], "sources": ["B:/Done projects/Healthcare/frontend/src/pages/Help.jsx"], "sourcesContent": ["function Help() {\r\n  return(\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-20\">\r\n      <div className=\"max-w-6xl mx-auto px-6\">\r\n        {/* Header Section */}\r\n        <div className=\"text-center mb-16\">\r\n          <span className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-semibold mb-4\">\r\n            💡 Support Center\r\n          </span>\r\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">Help Center</h1>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Find answers to common questions and get the support you need for our healthcare platform\r\n          </p>\r\n        </div>\r\n\r\n        {/* FAQ Section */}\r\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100\">\r\n          <div className=\"flex items-center mb-8\">\r\n            <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4\">\r\n              <span className=\"text-white text-xl\">❓</span>\r\n            </div>\r\n            <h2 className=\"text-3xl font-bold text-gray-900\">Frequently Asked Questions</h2>\r\n          </div>\r\n\r\n          <div className=\"space-y-8\">\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border-l-4 border-blue-500 hover:shadow-lg transition-all duration-300\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-4 mt-1\">\r\n                    <span className=\"text-white text-sm\">📅</span>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">How do I schedule an appointment?</h3>\r\n                    <p className=\"text-gray-700 leading-relaxed\">\r\n                      To schedule an appointment, navigate to the Appointments section from your dashboard,\r\n                      click on \"New Appointment,\" select your preferred healthcare provider, date, and time.\r\n                      You'll receive a confirmation email with all the details.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border-l-4 border-green-500 hover:shadow-lg transition-all duration-300\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1\">\r\n                    <span className=\"text-white text-sm\">📋</span>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">How can I access my medical records?</h3>\r\n                    <p className=\"text-gray-700 leading-relaxed\">\r\n                      Your medical records can be accessed from the Medical Records section. You can view,\r\n                      download, or share your records with healthcare providers as needed. All records are\r\n                      organized by date and medical specialty for easy navigation.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border-l-4 border-purple-500 hover:shadow-lg transition-all duration-300\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-4 mt-1\">\r\n                    <span className=\"text-white text-sm\">🔒</span>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Is my information secure?</h3>\r\n                    <p className=\"text-gray-700 leading-relaxed\">\r\n                      Yes, we take data security very seriously. All your information is encrypted and stored\r\n                      securely in compliance with HIPAA and other healthcare privacy regulations. We use\r\n                      industry-standard security measures to protect your data.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-r from-orange-50 to-orange-100 p-6 rounded-xl border-l-4 border-orange-500 hover:shadow-lg transition-all duration-300\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4 mt-1\">\r\n                    <span className=\"text-white text-sm\">💬</span>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">How do I contact my doctor?</h3>\r\n                    <p className=\"text-gray-700 leading-relaxed\">\r\n                      You can contact your doctor through the Messages section. Simply select your healthcare\r\n                      provider and send a secure message. For urgent matters, please call 112 for emergency\r\n                      or visit the nearest emergency room.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-r from-teal-50 to-teal-100 p-6 rounded-xl border-l-4 border-teal-500 hover:shadow-lg transition-all duration-300\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center mr-4 mt-1\">\r\n                    <span className=\"text-white text-sm\">👤</span>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">How do I update my personal information?</h3>\r\n                    <p className=\"text-gray-700 leading-relaxed\">\r\n                      To update your personal information, go to your Profile settings. You can update your\r\n                      contact information, insurance details, and emergency contacts. Some changes may require\r\n                      verification from our support team.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Support Cards */}\r\n        <div className=\"grid md:grid-cols-2 gap-8 mb-12\">\r\n          <div className=\"group\">\r\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\r\n              <div className=\"flex items-center mb-6\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4\">\r\n                  <span className=\"text-white text-xl\">🎧</span>\r\n                </div>\r\n                <h2 className=\"text-2xl font-bold text-blue-900\">Need More Help?</h2>\r\n              </div>\r\n              <p className=\"text-blue-800 mb-6 leading-relaxed\">\r\n                Our support team is available 24/7 to assist you with any questions or issues.\r\n                Get personalized help from our healthcare technology experts.\r\n              </p>\r\n              <div className=\"space-y-3\">\r\n                <button className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\">\r\n                  Contact Support\r\n                </button>\r\n                <button className=\"w-full bg-white hover:bg-blue-50 text-blue-600 font-semibold py-3 px-6 rounded-xl border-2 border-blue-300 hover:border-blue-400 transition-all duration-300\">\r\n                  View Documentation\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"group\">\r\n            <div className=\"bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-8 border border-red-200 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\r\n              <div className=\"flex items-center mb-6\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mr-4\">\r\n                  <span className=\"text-white text-xl\">🚨</span>\r\n                </div>\r\n                <h2 className=\"text-2xl font-bold text-red-900\">Emergency Contacts</h2>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center p-3 bg-white rounded-lg border border-red-200\">\r\n                  <span className=\"text-red-600 mr-3 text-lg\">🚑</span>\r\n                  <div>\r\n                    <p className=\"font-semibold text-red-800\">Emergency</p>\r\n                    <p className=\"text-red-700 text-lg font-bold\">112</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center p-3 bg-white rounded-lg border border-red-200\">\r\n                  <span className=\"text-red-600 mr-3 text-lg\">🏥</span>\r\n                  <div>\r\n                    <p className=\"font-semibold text-red-800\">Hospital</p>\r\n                    <p className=\"text-red-700\">(555) 123-4567</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center p-3 bg-white rounded-lg border border-red-200\">\r\n                  <span className=\"text-red-600 mr-3 text-lg\">☢️</span>\r\n                  <div>\r\n                    <p className=\"font-semibold text-red-800\">Poison Control</p>\r\n                    <p className=\"text-red-700\">1-************</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center p-3 bg-white rounded-lg border border-red-200\">\r\n                  <span className=\"text-red-600 mr-3 text-lg\">🧠</span>\r\n                  <div>\r\n                    <p className=\"font-semibold text-red-800\">Mental Health Crisis</p>\r\n                    <p className=\"text-red-700 text-lg font-bold\">988</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quick Links */}\r\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\r\n          <div className=\"flex items-center mb-8\">\r\n            <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-4\">\r\n              <span className=\"text-white text-xl\">🔗</span>\r\n            </div>\r\n            <h2 className=\"text-3xl font-bold text-gray-900\">Quick Links</h2>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-3 gap-6\">\r\n            <button className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105 border border-indigo-200 text-left w-full\">\r\n                <div className=\"flex items-center mb-4\">\r\n                  <div className=\"w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center mr-3\">\r\n                    <span className=\"text-white\">📖</span>\r\n                  </div>\r\n                  <h3 className=\"font-bold text-indigo-900 text-lg\">User Guide</h3>\r\n                </div>\r\n                <p className=\"text-indigo-700 leading-relaxed\">Complete guide to using the platform with detailed instructions</p>\r\n              </div>\r\n            </button>\r\n\r\n            <button className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-pink-50 to-pink-100 p-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105 border border-pink-200 text-left w-full\">\r\n                <div className=\"flex items-center mb-4\">\r\n                  <div className=\"w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center mr-3\">\r\n                    <span className=\"text-white\">🎥</span>\r\n                  </div>\r\n                  <h3 className=\"font-bold text-pink-900 text-lg\">Video Tutorials</h3>\r\n                </div>\r\n                <p className=\"text-pink-700 leading-relaxed\">Step-by-step video instructions for all platform features</p>\r\n              </div>\r\n            </button>\r\n\r\n            <button className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-emerald-50 to-emerald-100 p-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105 border border-emerald-200 text-left w-full\">\r\n                <div className=\"flex items-center mb-4\">\r\n                  <div className=\"w-10 h-10 bg-emerald-500 rounded-lg flex items-center justify-center mr-3\">\r\n                    <span className=\"text-white\">🛡️</span>\r\n                  </div>\r\n                  <h3 className=\"font-bold text-emerald-900 text-lg\">Privacy Policy</h3>\r\n                </div>\r\n                <p className=\"text-emerald-700 leading-relaxed\">How we protect your information and ensure data security</p>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6 mt-20\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\">\r\n            <div className=\"col-span-1 md:col-span-2\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\">\r\n                  <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold\">HEALTHCARE Portal</h3>\r\n              </div>\r\n              <p className=\"text-gray-300 mb-6 max-w-md leading-relaxed\">\r\n                Your trusted healthcare management platform providing comprehensive digital solutions for modern medical care and patient management.\r\n              </p>\r\n              <div className=\"flex space-x-4\">\r\n                <div className=\"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\">\r\n                  <span className=\"text-sm\">📧</span>\r\n                </div>\r\n                <div className=\"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\">\r\n                  <span className=\"text-sm\">📱</span>\r\n                </div>\r\n                <div className=\"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\">\r\n                  <span className=\"text-sm\">🌐</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\r\n              <ul className=\"space-y-2\">\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">About Us</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Services</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Doctors</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Contact</a></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div>\r\n              <h4 className=\"text-lg font-semibold mb-4\">Support</h4>\r\n              <ul className=\"space-y-2\">\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Help Center</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Privacy Policy</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Terms of Service</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Emergency: 112</a></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"border-t border-gray-700 pt-8 text-center\">\r\n            <p className=\"text-gray-400\">\r\n              &copy; 2025 HealthCarePro. All rights reserved. | Terms & Privacy\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Help;\r\n"], "mappings": ";;AAAA,SAASA,IAAIA,CAAA,EAAG;EACd,oBACEC,OAAA;IAAKC,SAAS,EAAC,8DAA8D;IAAAC,QAAA,gBAC3EF,OAAA;MAAKC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCF,OAAA;QAAKC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCF,OAAA;UAAMC,SAAS,EAAC,2HAA2H;UAAAC,QAAA,EAAC;QAE5I;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPN,OAAA;UAAIC,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFN,OAAA;UAAGC,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNN,OAAA;QAAKC,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC9EF,OAAA;UAAKC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCF,OAAA;YAAKC,SAAS,EAAC,wGAAwG;YAAAC,QAAA,eACrHF,OAAA;cAAMC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNN,OAAA;YAAIC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eAENN,OAAA;UAAKC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBF,OAAA;YAAKC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBF,OAAA;cAAKC,SAAS,EAAC,iIAAiI;cAAAC,QAAA,eAC9IF,OAAA;gBAAKC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BF,OAAA;kBAAKC,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAC1FF,OAAA;oBAAMC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNN,OAAA;kBAAKC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBF,OAAA;oBAAIC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAiC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3FN,OAAA;oBAAGC,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAC;kBAI7C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBF,OAAA;cAAKC,SAAS,EAAC,oIAAoI;cAAAC,QAAA,eACjJF,OAAA;gBAAKC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BF,OAAA;kBAAKC,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,eAC3FF,OAAA;oBAAMC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNN,OAAA;kBAAKC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBF,OAAA;oBAAIC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAoC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9FN,OAAA;oBAAGC,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAC;kBAI7C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBF,OAAA;cAAKC,SAAS,EAAC,uIAAuI;cAAAC,QAAA,eACpJF,OAAA;gBAAKC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BF,OAAA;kBAAKC,SAAS,EAAC,+EAA+E;kBAAAC,QAAA,eAC5FF,OAAA;oBAAMC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNN,OAAA;kBAAKC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBF,OAAA;oBAAIC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnFN,OAAA;oBAAGC,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAC;kBAI7C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBF,OAAA;cAAKC,SAAS,EAAC,uIAAuI;cAAAC,QAAA,eACpJF,OAAA;gBAAKC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BF,OAAA;kBAAKC,SAAS,EAAC,+EAA+E;kBAAAC,QAAA,eAC5FF,OAAA;oBAAMC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNN,OAAA;kBAAKC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBF,OAAA;oBAAIC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrFN,OAAA;oBAAGC,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAC;kBAI7C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENN,OAAA;YAAKC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBF,OAAA;cAAKC,SAAS,EAAC,iIAAiI;cAAAC,QAAA,eAC9IF,OAAA;gBAAKC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BF,OAAA;kBAAKC,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAC1FF,OAAA;oBAAMC,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNN,OAAA;kBAAKC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBF,OAAA;oBAAIC,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAwC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClGN,OAAA;oBAAGC,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAC;kBAI7C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNN,OAAA;QAAKC,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CF,OAAA;UAAKC,SAAS,EAAC,OAAO;UAAAC,QAAA,eACpBF,OAAA;YAAKC,SAAS,EAAC,+JAA+J;YAAAC,QAAA,gBAC5KF,OAAA;cAAKC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCF,OAAA;gBAAKC,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,eACrHF,OAAA;kBAAMC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNN,OAAA;gBAAIC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNN,OAAA;cAAGC,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAGlD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJN,OAAA;cAAKC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBF,OAAA;gBAAQC,SAAS,EAAC,8NAA8N;gBAAAC,QAAA,EAAC;cAEjP;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTN,OAAA;gBAAQC,SAAS,EAAC,8JAA8J;gBAAAC,QAAA,EAAC;cAEjL;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENN,OAAA;UAAKC,SAAS,EAAC,OAAO;UAAAC,QAAA,eACpBF,OAAA;YAAKC,SAAS,EAAC,4JAA4J;YAAAC,QAAA,gBACzKF,OAAA;cAAKC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCF,OAAA;gBAAKC,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,eACnHF,OAAA;kBAAMC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNN,OAAA;gBAAIC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACNN,OAAA;cAAKC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBF,OAAA;gBAAKC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAC9EF,OAAA;kBAAMC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAGC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACvDN,OAAA;oBAAGC,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAC9EF,OAAA;kBAAMC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAGC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACtDN,OAAA;oBAAGC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAC9EF,OAAA;kBAAMC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAGC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5DN,OAAA;oBAAGC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAC9EF,OAAA;kBAAMC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDN,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAGC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClEN,OAAA;oBAAGC,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNN,OAAA;QAAKC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEF,OAAA;UAAKC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCF,OAAA;YAAKC,SAAS,EAAC,4GAA4G;YAAAC,QAAA,eACzHF,OAAA;cAAMC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNN,OAAA;YAAIC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAENN,OAAA;UAAKC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCF,OAAA;YAAQC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACvBF,OAAA;cAAKC,SAAS,EAAC,2KAA2K;cAAAC,QAAA,gBACxLF,OAAA;gBAAKC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCF,OAAA;kBAAKC,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,eACvFF,OAAA;oBAAMC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNN,OAAA;kBAAIC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNN,OAAA;gBAAGC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAA+D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAETN,OAAA;YAAQC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACvBF,OAAA;cAAKC,SAAS,EAAC,qKAAqK;cAAAC,QAAA,gBAClLF,OAAA;gBAAKC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCF,OAAA;kBAAKC,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,eACrFF,OAAA;oBAAMC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNN,OAAA;kBAAIC,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNN,OAAA;gBAAGC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAyD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAETN,OAAA;YAAQC,SAAS,EAAC,OAAO;YAAAC,QAAA,eACvBF,OAAA;cAAKC,SAAS,EAAC,8KAA8K;cAAAC,QAAA,gBAC3LF,OAAA;gBAAKC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCF,OAAA;kBAAKC,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,eACxFF,OAAA;oBAAMC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNN,OAAA;kBAAIC,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNN,OAAA;gBAAGC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNN,OAAA;MAAQC,SAAS,EAAC,wEAAwE;MAAAC,QAAA,eACxFF,OAAA;QAAKC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCF,OAAA;UAAKC,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDF,OAAA;YAAKC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCF,OAAA;cAAKC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCF,OAAA;gBAAKC,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,eACrHF,OAAA;kBAAKC,SAAS,EAAC,oBAAoB;kBAACM,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAN,QAAA,eACzEF,OAAA;oBAAMS,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,qLAAqL;oBAACC,QAAQ,EAAC;kBAAS;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNN,OAAA;gBAAIC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNN,OAAA;cAAGC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJN,OAAA;cAAKC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BF,OAAA;gBAAKC,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,eACrIF,OAAA;kBAAMC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,eACrIF,OAAA;kBAAMC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNN,OAAA;gBAAKC,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,eACrIF,OAAA;kBAAMC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENN,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAIC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DN,OAAA;cAAIC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBF,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9FN,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9FN,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FN,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENN,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAIC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDN,OAAA;cAAIC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBF,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGN,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpGN,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtGN,OAAA;gBAAAE,QAAA,eAAIF,OAAA;kBAAGY,IAAI,EAAC,GAAG;kBAACX,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENN,OAAA;UAAKC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxDF,OAAA;YAAGC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACO,EAAA,GApSQd,IAAI;AAsSb,eAAeA,IAAI;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}