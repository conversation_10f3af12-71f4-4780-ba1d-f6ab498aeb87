// Test script for Add User and Add Doctor API endpoints
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000';

// Test data for user creation
const testUser = {
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'password123',
  role: 'staff',
  userType: 'staff',
  department: 'Administration',
  specialization: '',
  phone: '+**********'
};

// Test data for doctor creation
const testDoctor = {
  firstName: 'Dr. <PERSON>',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+**********',
  specialization: 'Cardiology',
  department: 'Cardiology',
  licenseNumber: 'MD123456',
  experience: '5',
  education: 'MD from Harvard Medical School',
  consultationFee: '150.00'
};

async function testCreateUser() {
  console.log('\n=== Testing POST /api/users ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser),
    });

    const data = await response.json();
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ User created successfully!');
      return data.data.id;
    } else {
      console.log('❌ Failed to create user:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Error testing user creation:', error.message);
    return null;
  }
}

async function testCreateDoctor() {
  console.log('\n=== Testing POST /api/doctors ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/doctors`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testDoctor),
    });

    const data = await response.json();
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Doctor created successfully!');
      return data.data.id;
    } else {
      console.log('❌ Failed to create doctor:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Error testing doctor creation:', error.message);
    return null;
  }
}

async function testValidation() {
  console.log('\n=== Testing Validation ===');
  
  // Test user validation - missing required fields
  console.log('\n--- Testing user validation (missing fields) ---');
  try {
    const response = await fetch(`${BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName: 'Test',
        // Missing required fields
      }),
    });

    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data.message);
    
    if (response.status === 400) {
      console.log('✅ User validation working correctly');
    } else {
      console.log('❌ User validation not working as expected');
    }
  } catch (error) {
    console.log('❌ Error testing user validation:', error.message);
  }

  // Test doctor validation - missing required fields
  console.log('\n--- Testing doctor validation (missing fields) ---');
  try {
    const response = await fetch(`${BASE_URL}/api/doctors`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName: 'Test',
        // Missing required fields
      }),
    });

    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', data.message);
    
    if (response.status === 400) {
      console.log('✅ Doctor validation working correctly');
    } else {
      console.log('❌ Doctor validation not working as expected');
    }
  } catch (error) {
    console.log('❌ Error testing doctor validation:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting API Tests...');
  
  // Test validation first
  await testValidation();
  
  // Test successful creation
  const userId = await testCreateUser();
  const doctorId = await testCreateDoctor();
  
  console.log('\n=== Test Summary ===');
  console.log('User created:', userId ? `✅ ID: ${userId}` : '❌ Failed');
  console.log('Doctor created:', doctorId ? `✅ ID: ${doctorId}` : '❌ Failed');
  
  console.log('\n🎉 Tests completed!');
}

// Run the tests
runTests().catch(console.error);
