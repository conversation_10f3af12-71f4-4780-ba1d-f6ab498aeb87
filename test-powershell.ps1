# PowerShell script to test the API endpoints

$baseUrl = "http://localhost:5000"

# Test user creation
Write-Host "=== Testing POST /api/users ===" -ForegroundColor Yellow

$userBody = @{
    firstName = "John"
    lastName = "Doe"
    email = "<EMAIL>"
    password = "password123"
    role = "staff"
    userType = "staff"
    department = "Administration"
    phone = "+1234567890"
} | ConvertTo-Json

try {
    $userResponse = Invoke-RestMethod -Uri "$baseUrl/api/users" -Method POST -Body $userBody -ContentType "application/json"
    Write-Host "✅ User created successfully!" -ForegroundColor Green
    Write-Host "User ID: $($userResponse.data.id)" -ForegroundColor Green
    Write-Host "Response: $($userResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create user: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

Write-Host ""

# Test doctor creation
Write-Host "=== Testing POST /api/doctors ===" -ForegroundColor Yellow

$doctorBody = @{
    firstName = "Dr. Jane"
    lastName = "Smith"
    email = "<EMAIL>"
    phone = "+**********"
    specialization = "Cardiology"
    department = "Cardiology"
    licenseNumber = "MD123456"
    experience = "5"
    education = "MD from Harvard Medical School"
    consultationFee = "150.00"
} | ConvertTo-Json

try {
    $doctorResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctors" -Method POST -Body $doctorBody -ContentType "application/json"
    Write-Host "✅ Doctor created successfully!" -ForegroundColor Green
    Write-Host "Doctor ID: $($doctorResponse.data.id)" -ForegroundColor Green
    Write-Host "Response: $($doctorResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create doctor: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

Write-Host ""

# Test validation - missing fields
Write-Host "=== Testing Validation ===" -ForegroundColor Yellow

$invalidUserBody = @{
    firstName = "Test"
    # Missing required fields
} | ConvertTo-Json

try {
    $validationResponse = Invoke-RestMethod -Uri "$baseUrl/api/users" -Method POST -Body $invalidUserBody -ContentType "application/json"
    Write-Host "❌ Validation not working - should have failed" -ForegroundColor Red
} catch {
    Write-Host "✅ User validation working correctly" -ForegroundColor Green
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🎉 Tests completed!" -ForegroundColor Green
