const express = require('express');
const cors = require('cors');
const app = express();
const port = 5001; // Use different port

// Middleware
app.use(cors());
app.use(express.json());

// Debug middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Test route
app.get('/api/test', (req, res) => {
  res.json({ success: true, message: 'GET test route working' });
});

// Test POST route
app.post('/api/test', (req, res) => {
  res.json({ success: true, message: 'POST test route working', body: req.body });
});

// POST create new user (simplified)
app.post('/api/users', (req, res) => {
  console.log('POST /api/users called with body:', req.body);
  
  const {
    firstName,
    lastName,
    email,
    password,
    role,
    userType
  } = req.body;

  // Basic validation
  if (!firstName || !lastName || !email || !password || !role || !userType) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: firstName, lastName, email, password, role, userType'
    });
  }

  // Simulate successful creation
  res.status(201).json({
    success: true,
    message: 'User created successfully (test mode)',
    data: {
      id: 999,
      firstName,
      lastName,
      email,
      role,
      userType
    }
  });
});

// POST create new doctor (simplified)
app.post('/api/doctors', (req, res) => {
  console.log('POST /api/doctors called with body:', req.body);
  
  const {
    firstName,
    lastName,
    email,
    phone,
    specialization,
    department
  } = req.body;

  // Basic validation
  if (!firstName || !lastName || !email || !phone || !specialization || !department) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: firstName, lastName, email, phone, specialization, department'
    });
  }

  // Simulate successful creation
  res.status(201).json({
    success: true,
    message: 'Doctor created successfully (test mode)',
    data: {
      id: 888,
      doctorId: 'DOC123456',
      firstName,
      lastName,
      email,
      phone,
      specialization,
      department
    }
  });
});

// Start server
app.listen(port, () => {
  console.log(`Test server running on port ${port}`);
});
